# Create a group for CI/CD users (push images)
resource "oci_identity_group" "push_group" {
  name           = "${var.project}-images-push"
  description    = "Group for ${var.project} users who push images"
  compartment_id = var.tenancy_ocid
}

# Create a user for CI/CD operations (push images)
resource "oci_identity_user" "push_user" {
  name           = "${var.project}-images-push"
  description    = "User for ${var.project} push operations"
  compartment_id = var.tenancy_ocid
  email          = var.user_email
}

# Add the push user to the push group
resource "oci_identity_user_group_membership" "push_user_group_membership" {
  user_id  = oci_identity_user.push_user.id
  group_id = oci_identity_group.push_group.id
}

# Create an auth token for the push user (for Docker login)
resource "oci_identity_auth_token" "push_user_token" {
  description = "Auth token for ${var.project} Docker push login"
  user_id     = oci_identity_user.push_user.id
}

# Create a policy to allow the push group to manage repositories in the compartment
resource "oci_identity_policy" "push_policy" {
  name           = "${var.project}-images-push"
  description    = "Policy for ${var.project} push operations"
  compartment_id = var.tenancy_ocid
  statements     = [
    "Allow group ${oci_identity_group.push_group.name} to manage repos in compartment ${data.oci_identity_compartment.compartment.name}",
    "Allow group ${oci_identity_group.push_group.name} to read objectstorage-namespaces in tenancy",
    "Allow group ${oci_identity_group.push_group.name} to manage objects in compartment ${data.oci_identity_compartment.compartment.name}"
  ]
}
