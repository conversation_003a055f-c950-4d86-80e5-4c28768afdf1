# Create a group for image pulling
resource "oci_identity_group" "pull_group" {
  name           = "${var.project}-images-pull"
  description    = "Group for ${var.project} users who pull images"
  compartment_id = var.tenancy_ocid
}

# Create a user for image pulling operations
resource "oci_identity_user" "pull_user" {
  name           = "${var.project}-images-pull"
  description    = "User for ${var.project} pull operations"
  compartment_id = var.tenancy_ocid
  email          = var.user_email
}

# Add the pull user to the pull group
resource "oci_identity_user_group_membership" "pull_user_group_membership" {
  user_id  = oci_identity_user.pull_user.id
  group_id = oci_identity_group.pull_group.id
}

# Create an auth token for the pull user (for Docker login)
resource "oci_identity_auth_token" "pull_user_token" {
  description = "Auth token for ${var.project} Docker pull login"
  user_id     = oci_identity_user.pull_user.id
}

# Create a policy to allow the pull group to pull images from repositories in the compartment
resource "oci_identity_policy" "pull_policy" {
  name           = "${var.project}-images-pull"
  description    = "Policy for ${var.project} pull operations"
  compartment_id = var.tenancy_ocid
  statements     = [
    "Allow group ${oci_identity_group.pull_group.name} to read repos in compartment ${data.oci_identity_compartment.compartment.name}",
    "Allow group ${oci_identity_group.pull_group.name} to read objectstorage-namespaces in tenancy",
    "Allow group ${oci_identity_group.pull_group.name} to read objects in compartment ${data.oci_identity_compartment.compartment.name}"
  ]
}
