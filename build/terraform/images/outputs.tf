output "compartment_id" {
  value       = var.compartment_id
  description = "OCID of the used compartment"
}

output "compartment_name" {
  value       = data.oci_identity_compartment.compartment.name
  description = "Name of the used compartment"
}

output "push_user_id" {
  value       = oci_identity_user.push_user.id
  description = "OCID of the push user"
}

output "push_user_name" {
  value       = oci_identity_user.push_user.name
  description = "Name of the push user"
}

output "pull_user_id" {
  value       = oci_identity_user.pull_user.id
  description = "OCID of the pull user"
}

output "pull_user_name" {
  value       = oci_identity_user.pull_user.name
  description = "Name of the pull user"
}

output "registry_base_url" {
  value       = "${var.region}.ocir.io/${data.oci_objectstorage_namespace.ns.namespace}"
  description = "Base URL for the container registry"
}

output "producer_repository_url" {
  value       = "${var.region}.ocir.io/${data.oci_objectstorage_namespace.ns.namespace}/${oci_artifacts_container_repository.producer_repository.display_name}"
  description = "URL for the producer repository"
}

output "consumer_repository_url" {
  value       = "${var.region}.ocir.io/${data.oci_objectstorage_namespace.ns.namespace}/${oci_artifacts_container_repository.consumer_repository.display_name}"
  description = "URL for the consumer repository"
}

output "push_auth_token" {
  value       = oci_identity_auth_token.push_user_token.token
  description = "Authentication token for Docker push login"
  sensitive   = true
}

output "pull_auth_token" {
  value       = oci_identity_auth_token.pull_user_token.token
  description = "Authentication token for Docker pull login"
  sensitive   = true
}