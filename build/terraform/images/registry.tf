# Create OCIR repositories for the services
resource "oci_artifacts_container_repository" "producer_repository" {
  compartment_id = var.compartment_id
  display_name   = "${var.project}/producer"
  is_public      = false
}

resource "oci_artifacts_container_repository" "consumer_repository" {
  compartment_id = var.compartment_id
  display_name   = "${var.project}/consumer"
  is_public      = false
}
