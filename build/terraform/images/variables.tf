variable "tenancy_ocid" {
  description = "OCID of your tenancy"
  type        = string
}

variable "user_ocid" {
  description = "OCID of your user"
  type        = string
}

variable "fingerprint" {
  description = "Fingerprint for your API key"
  type        = string
}

variable "private_key_path" {
  description = "Path to your private key file"
  type        = string
}

variable "region" {
  description = "The region to deploy to"
  type        = string
}

variable "compartment_id" {
  description = "OCID of the existing compartment to use"
  type        = string
}

variable "user_email" {
  description = "Email for users (can be a placeholder)"
  type        = string
  default     = "<EMAIL>"
}

variable "project" {
  description = "Prefix for repository names"
  type        = string
  default     = "project-name"
}