{"version": 4, "terraform_version": "1.11.0", "serial": 57, "lineage": "a51e85a9-f9c2-52ed-a1ff-abf893877279", "outputs": {"compartment_id": {"value": "ocid1.compartment.oc1..aaaaaaaacer2hwodjsoppqkg4ateqmxa3x4gpgkqj7lnxwr2xrqpjutwmrkq", "type": "string"}, "compartment_name": {"value": "IkatecLabs", "type": "string"}, "consumer_repository_url": {"value": "sa-vinhedo-1.ocir.io/axvaplbwrlcl/message-broker/consumer", "type": "string"}, "producer_repository_url": {"value": "sa-vinhedo-1.ocir.io/axvaplbwrlcl/message-broker/producer", "type": "string"}, "pull_auth_token": {"value": "IZjKd-:l(mweN))Aod#7", "type": "string", "sensitive": true}, "pull_user_id": {"value": "ocid1.user.oc1..aaaaaaaa6li546p5sipmxt5wm3hsx3q3wxm3kob3q4fmbvc2ff3oh7stihxa", "type": "string"}, "pull_user_name": {"value": "message-broker-images-pull", "type": "string"}, "push_auth_token": {"value": "vb6-]_nAbf_78N>ETU3v", "type": "string", "sensitive": true}, "push_user_id": {"value": "ocid1.user.oc1..aaaaaaaapcmn6ql775xfrlyh3ksz3rrxddj7eqo4xoulxeprftvcbimnuqeq", "type": "string"}, "push_user_name": {"value": "message-broker-images-push", "type": "string"}, "registry_base_url": {"value": "sa-vinhedo-1.ocir.io/axvaplbwrlcl", "type": "string"}}, "resources": [{"mode": "data", "type": "oci_identity_compartment", "name": "compartment", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-04-18T21:58:00.127Z"}, "description": "Compartimento para projetos do IkatecLabs", "freeform_tags": {}, "id": "ocid1.compartment.oc1..aaaaaaaacer2hwodjsoppqkg4ateqmxa3x4gpgkqj7lnxwr2xrqpjutwmrkq", "inactive_state": null, "is_accessible": true, "name": "IkatecLabs", "state": "ACTIVE", "time_created": "2025-04-18 21:58:00.194 +0000 UTC"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "oci_objectstorage_namespace", "name": "ns", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "id": "ObjectStorageNamespaceDataSource-474279189", "namespace": "axvaplbwrlcl"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "oci_artifacts_container_repository", "name": "consumer_repository", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"billable_size_in_gbs": "0", "compartment_id": "ocid1.compartment.oc1..aaaaaaaacer2hwodjsoppqkg4ateqmxa3x4gpgkqj7lnxwr2xrqpjutwmrkq", "created_by": "ocid1.user.oc1..aaaaaaaaeuyxmxdbk57x4kq2ktim2kbc5d2dhx5x3kqqx73nd3ceczeag5tq", "defined_tags": {}, "display_name": "message-broker/consumer", "freeform_tags": {}, "id": "ocid1.containerrepo.oc1.sa-vinhedo-1.0.axvaplbwrlcl.aaaaaaaasrcptmus5pbtodhivlnxlraxnyjgwo6z74rau2qtqurwaqw5aw6q", "image_count": 0, "is_immutable": false, "is_public": false, "layer_count": 0, "layers_size_in_bytes": "0", "namespace": "axvaplbwrlcl", "readme": [], "state": "AVAILABLE", "system_tags": {}, "time_created": "2025-04-19 03:47:49.08 +0000 UTC", "time_last_pushed": null, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "oci_artifacts_container_repository", "name": "producer_repository", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"billable_size_in_gbs": "0", "compartment_id": "ocid1.compartment.oc1..aaaaaaaacer2hwodjsoppqkg4ateqmxa3x4gpgkqj7lnxwr2xrqpjutwmrkq", "created_by": "ocid1.user.oc1..aaaaaaaaeuyxmxdbk57x4kq2ktim2kbc5d2dhx5x3kqqx73nd3ceczeag5tq", "defined_tags": {}, "display_name": "message-broker/producer", "freeform_tags": {}, "id": "ocid1.containerrepo.oc1.sa-vinhedo-1.0.axvaplbwrlcl.aaaaaaaaczal2dw6euk3xfw4i2uq7ckb4w4bbam4lt6mivt4tcofrz3dioqq", "image_count": 0, "is_immutable": false, "is_public": false, "layer_count": 0, "layers_size_in_bytes": "0", "namespace": "axvaplbwrlcl", "readme": [], "state": "AVAILABLE", "system_tags": {}, "time_created": "2025-04-19 03:47:49.084 +0000 UTC", "time_last_pushed": null, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "oci_identity_auth_token", "name": "pull_user_token", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"description": "Auth token for message-broker <PERSON><PERSON> pull login", "id": "ocid1.credential.oc1..aaaaaaaaztfj2x4ocer4tczrz5armpfmhokmksrk7pu4arev25oj5kugwhna", "inactive_state": null, "state": "ACTIVE", "time_created": "2025-04-19 03:47:49.773 +0000 UTC", "time_expires": null, "timeouts": null, "token": "IZjKd-:l(mweN))Aod#7", "user_id": "ocid1.user.oc1..aaaaaaaa6li546p5sipmxt5wm3hsx3q3wxm3kob3q4fmbvc2ff3oh7stihxa"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["oci_identity_user.pull_user"]}]}, {"mode": "managed", "type": "oci_identity_auth_token", "name": "push_user_token", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"description": "Auth token for message-broker <PERSON><PERSON> push login", "id": "ocid1.credential.oc1..aaaaaaaayttphl3vgxgyphndpzyfrhond57n366n2ie5ieudqqqd3qxpt2nq", "inactive_state": null, "state": "ACTIVE", "time_created": "2025-04-19 03:47:49.776 +0000 UTC", "time_expires": null, "timeouts": null, "token": "vb6-]_nAbf_78N>ETU3v", "user_id": "ocid1.user.oc1..aaaaaaaapcmn6ql775xfrlyh3ksz3rrxddj7eqo4xoulxeprftvcbimnuqeq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["oci_identity_user.push_user"]}]}, {"mode": "managed", "type": "oci_identity_group", "name": "pull_group", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-04-19T03:47:49.052Z"}, "description": "Group for message-broker users who pull images", "freeform_tags": {}, "id": "ocid1.group.oc1..aaaaaaaa43gyv4ikcwdoxwxmyogbreqrogfsctmzdvd7fi3lsmvqlhrbwy2q", "inactive_state": null, "name": "message-broker-images-pull", "state": "ACTIVE", "time_created": "2025-04-19 03:47:49.078 +0000 UTC", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "oci_identity_group", "name": "push_group", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-04-19T03:47:49.058Z"}, "description": "Group for message-broker users who push images", "freeform_tags": {}, "id": "ocid1.group.oc1..aaaaaaaa5wstb6w5leuitkavpdv5wziyvxla6vvshhtigqxazmufrq5me7tq", "inactive_state": null, "name": "message-broker-images-push", "state": "ACTIVE", "time_created": "2025-04-19 03:47:49.084 +0000 UTC", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "oci_identity_policy", "name": "pull_policy", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"ETag": "ceccf9366f69b5bf69e1dc8dae07c767a18b5c35", "compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-04-19T03:47:51.595Z"}, "description": "Policy for message-broker pull operations", "freeform_tags": {}, "id": "ocid1.policy.oc1..aaaaaaaa4cw5dwuz6os4fjxxw3v5ld7yuoi46i7uofit252r65o4bc6go54a", "inactive_state": null, "lastUpdateETag": "ceccf9366f69b5bf69e1dc8dae07c767a18b5c35", "name": "message-broker-images-pull", "policyHash": "cb4c00c4c249436c1c1e97ff7b0eb7bc", "state": "ACTIVE", "statements": ["Allow group message-broker-images-pull to read repos in compartment IkatecLabs", "Allow group message-broker-images-pull to read objectstorage-namespaces in tenancy", "Allow group message-broker-images-pull to read objects in compartment IkatecLabs"], "time_created": "2025-04-19 03:47:51.613 +0000 UTC", "timeouts": null, "version_date": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["data.oci_identity_compartment.compartment", "oci_identity_group.pull_group"]}]}, {"mode": "managed", "type": "oci_identity_policy", "name": "push_policy", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"ETag": "da570b952192265d5c7e0019f7f75f111173a408", "compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-04-19T03:47:51.475Z"}, "description": "Policy for message-broker push operations", "freeform_tags": {}, "id": "ocid1.policy.oc1..aaaaaaaauhivoj66cjt7mqeze2pcr4tssakps3tuhg56jmsdht6fl5jqyapa", "inactive_state": null, "lastUpdateETag": "da570b952192265d5c7e0019f7f75f111173a408", "name": "message-broker-images-push", "policyHash": "29a0769a8ffa600015f2d9dd8093660c", "state": "ACTIVE", "statements": ["Allow group message-broker-images-push to manage repos in compartment IkatecLabs", "Allow group message-broker-images-push to read objectstorage-namespaces in tenancy", "Allow group message-broker-images-push to manage objects in compartment IkatecLabs"], "time_created": "2025-04-19 03:47:51.493 +0000 UTC", "timeouts": null, "version_date": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["data.oci_identity_compartment.compartment", "oci_identity_group.push_group"]}]}, {"mode": "managed", "type": "oci_identity_user", "name": "pull_user", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"capabilities": [{"can_use_api_keys": true, "can_use_auth_tokens": true, "can_use_console_password": true, "can_use_customer_secret_keys": true, "can_use_db_credentials": true, "can_use_oauth2client_credentials": true, "can_use_smtp_credentials": true}], "compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "db_user_name": null, "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-04-19T03:47:49.237Z"}, "description": "User for message-broker pull operations", "email": "<EMAIL>", "email_verified": false, "external_identifier": "4cbdc3b7c6b347eca633e3db56e989d8", "freeform_tags": {}, "id": "ocid1.user.oc1..aaaaaaaa6li546p5sipmxt5wm3hsx3q3wxm3kob3q4fmbvc2ff3oh7stihxa", "identity_provider_id": null, "inactive_state": null, "last_successful_login_time": null, "name": "message-broker-images-pull", "previous_successful_login_time": null, "state": "ACTIVE", "time_created": "2025-04-19 03:47:49.268 +0000 UTC", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "oci_identity_user", "name": "push_user", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"capabilities": [{"can_use_api_keys": true, "can_use_auth_tokens": true, "can_use_console_password": true, "can_use_customer_secret_keys": true, "can_use_db_credentials": true, "can_use_oauth2client_credentials": true, "can_use_smtp_credentials": true}], "compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "db_user_name": null, "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-04-19T03:47:49.219Z"}, "description": "User for message-broker push operations", "email": "<EMAIL>", "email_verified": false, "external_identifier": "57d5f8a75ef9427eb8992eb709f21f6c", "freeform_tags": {}, "id": "ocid1.user.oc1..aaaaaaaapcmn6ql775xfrlyh3ksz3rrxddj7eqo4xoulxeprftvcbimnuqeq", "identity_provider_id": null, "inactive_state": null, "last_successful_login_time": null, "name": "message-broker-images-push", "previous_successful_login_time": null, "state": "ACTIVE", "time_created": "2025-04-19 03:47:49.249 +0000 UTC", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "oci_identity_user_group_membership", "name": "pull_user_group_membership", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "group_id": "ocid1.group.oc1..aaaaaaaa43gyv4ikcwdoxwxmyogbreqrogfsctmzdvd7fi3lsmvqlhrbwy2q", "id": "ocid1.groupmembership.oc1..aaaaaaaa6q33wcf5jtz3jnt62qag7v6awommdcl7bearkurqbzcneq52rtta", "inactive_state": null, "state": "ACTIVE", "time_created": "2025-04-19 03:47:51.897 +0000 UTC", "timeouts": null, "user_id": "ocid1.user.oc1..aaaaaaaa6li546p5sipmxt5wm3hsx3q3wxm3kob3q4fmbvc2ff3oh7stihxa"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["oci_identity_group.pull_group", "oci_identity_user.pull_user"]}]}, {"mode": "managed", "type": "oci_identity_user_group_membership", "name": "push_user_group_membership", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "group_id": "ocid1.group.oc1..aaaaaaaa5wstb6w5leuitkavpdv5wziyvxla6vvshhtigqxazmufrq5me7tq", "id": "ocid1.groupmembership.oc1..aaaaaaaafk62awzbhf7mqet3xie5clu3nhbxyft6uoxtmubsasrf5rstna2a", "inactive_state": null, "state": "ACTIVE", "time_created": "2025-04-19 03:47:51.886 +0000 UTC", "timeouts": null, "user_id": "ocid1.user.oc1..aaaaaaaapcmn6ql775xfrlyh3ksz3rrxddj7eqo4xoulxeprftvcbimnuqeq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["oci_identity_group.push_group", "oci_identity_user.push_user"]}]}], "check_results": null}