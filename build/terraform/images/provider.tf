terraform {
  required_providers {
    oci = {
      source  = "oracle/oci"
      version = "6.20.0"
    }
  }
}

# Provider configuration
provider "oci" {
  tenancy_ocid     = var.tenancy_ocid
  user_ocid        = var.user_ocid
  fingerprint      = var.fingerprint
  private_key_path = var.private_key_path
  region           = var.region
}

# Common data sources
data "oci_objectstorage_namespace" "ns" {
  compartment_id = var.tenancy_ocid
}

# Use existing compartment instead of creating one
data "oci_identity_compartment" "compartment" {
  id = var.compartment_id
}
