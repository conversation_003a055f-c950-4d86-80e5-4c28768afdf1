# Build stage
FROM golang:1.23 AS builder

WORKDIR /go/src

# Copy the entire workspace structure
COPY src/go.work ./
COPY src/common ./common
COPY src/producer ./producer
COPY src/consumer ./consumer
COPY src/test ./test

WORKDIR /go/src/producer

# Build the application with workspace awareness
# Support for both AMD64 and ARM64 architectures
ARG TARGETARCH
RUN echo "Building for architecture: $TARGETARCH"

# List the source files to verify they're copied correctly
RUN find . -type f -name "*.go" | sort

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=$TARGETARCH go build -a -installsuffix cgo -o producer .

# Verify the binary was built correctly
RUN ls -la producer
# Check if the binary is executable
RUN test -x producer

# Final stage
# The distroless image supports multi-architecture out of the box
FROM gcr.io/distroless/static-debian12

# Copy the binary from builder
COPY --from=builder /go/src/producer/producer /app/producer
# Copy certificates for HTTPS connections
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Expose port
EXPOSE 2000

# Run the application
CMD ["/app/producer"]