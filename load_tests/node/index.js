const axios = require('axios');
const http = require('http');
const crypto = require('crypto');

// Simple configuration
const config = {
    url: 'http://127.0.0.1:2000/dispatch',
    numRequests: 5000
};

// Create HTTP agent to allow many concurrent requests
const httpAgent = new http.Agent({
    keepAlive: true,
    maxSockets: Infinity
});

// Track stats
let successCount = 0;
let totalRequests = 0;

// Function to generate a random message ID
function generateRandomMessageId() {
    return `message_${crypto.randomBytes(4).toString('hex')}`;
}

// Make a single request
async function makeRequest() {
    const messageId = generateRandomMessageId();

    const payload = {
        topic: "message.created",
        hashKey: messageId,
        payload: { id: messageId }
    };

    try {
        const response = await axios.post(config.url, payload, {
            httpAgent,
            headers: { 'Content-Type': 'application/json' },
            validateStatus: () => true // Don't throw on any status code
        });

        totalRequests++;
        if (response.status === 200) {
            successCount++;
        }

        return {
            messageId,
            status: response.status
        };
    } catch (error) {
        totalRequests++;
        return {
            messageId,
            status: 'error',
            error: error.message
        };
    }
}

// Run all requests
async function runAllRequests() {
    console.log(`Starting ${config.numRequests} requests to ${config.url}`);

    const startTime = Date.now();

    // Create array of request promises
    const requests = [];
    for (let i = 0; i < config.numRequests; i++) {
        requests.push(makeRequest());
        // await makeRequest()
    }

    // Wait for all requests to complete
    await Promise.all(requests);

    const endTime = Date.now();
    const totalTime = endTime - startTime;

    // Calculate success percentage
    const successPercentage = (successCount / totalRequests) * 100;

    // Display results
    console.log(`\n--- RESULTS ---`);
    console.log(`Total requests: ${totalRequests}`);
    console.log(`Successful (200) responses: ${successCount}`);
    console.log(`Success percentage: ${successPercentage.toFixed(2)}%`);
    console.log(`Total time: ${totalTime}ms`);
    console.log(`Requests per second: ${(totalRequests / (totalTime / 1000)).toFixed(2)}`);
}

// Run the program
runAllRequests().catch(error => {
    console.error('Fatal error:', error);
});