import http from 'k6/http';
import { check, sleep } from 'k6';
import { randomString } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';

// Configuration
export const options = {
    vus: 100,           // Number of virtual users
    iterations: 100000,   // Total number of requests to make
    thresholds: {
        'http_req_duration': ['p(95)<500'], // 95% of requests should be below 500ms
        'http_req_failed': ['rate<0.01'],   // Less than 1% of requests should fail
    },
};

// The default function that k6 will call for each virtual user
export default function() {
    // Generate a random message ID
    const messageId = randomString(8)

    const payload = JSON.stringify({
        topic: "message.created",
        hashKey: messageId,
        payload: { id: messageId }
    });

    const params = {
        headers: {
            'Content-Type': 'application/json',
        },
    };

    // Make the request
    const response = http.post('http://127.0.0.1:2000/dispatch', payload, params);

    // Check if the request was successful
    check(response, {
        'status is 200': (r) => r.status === 200,
    });
}