.PHONY: test test-unit test-integration test-all build clean

# Default target
all: test build

# Build the producer and consumer binaries
build:
	@echo "Building producer..."
	cd src/producer && go build -o ../../bin/producer
	@echo "Building consumer..."
	cd src/consumer && go build -o ../../bin/consumer

# Run unit tests only
test-unit:
	@echo "Running unit tests..."
	cd src && go work sync && go test -v ./common/... ./producer/... ./consumer/... -short

# Run integration tests only
test-integration:
	@echo "Running integration tests..."
	cd src && go work sync && go test -v ./test/integration/...

# Run all tests
test-all: test-unit test-integration

# Default test target runs unit tests only
test: test-unit

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf bin/
	go clean ./...

# Run the producer
run-producer:
	@echo "Running producer..."
	cd src/producer && go run main.go --addr=:2000

# Run the consumer
run-consumer:
	@echo "Running consumer..."
	cd src/consumer && go run main.go --config=../test/config/test-config.yaml

# Run load tests
load-test:
	@echo "Running load tests..."
	cd load_tests/k6 && k6 run dispatch.js

# Install dependencies
deps:
	@echo "Installing dependencies..."
	go mod download

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Run linter
lint:
	@echo "Running linter..."
	cd src && golangci-lint run ./common/... ./consumer/... ./producer/... ./test/... || true

# Generate test coverage report
coverage:
	@echo "Generating test coverage report..."
	cd src && go work sync && go test -coverprofile=../coverage.out ./common/... ./producer/... ./consumer/... -short
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated at coverage.html"

# Help target
help:
	@echo "Available targets:"
	@echo "  all            - Run tests and build binaries (default)"
	@echo "  build          - Build producer and consumer binaries"
	@echo "  test           - Run unit tests only"
	@echo "  test-unit      - Run unit tests only"
	@echo "  test-integration - Run integration tests only"
	@echo "  test-all       - Run all tests (unit and integration)"
	@echo "  clean          - Clean build artifacts"
	@echo "  run-producer   - Run the producer service"
	@echo "  run-consumer   - Run the consumer service"
	@echo "  load-test      - Run load tests"
	@echo "  deps           - Install dependencies"
	@echo "  fmt            - Format code"
	@echo "  lint           - Run linter"
	@echo "  coverage       - Generate test coverage report"
	@echo "  help           - Show this help message"
