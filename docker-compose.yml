x-golang-service: &golang-service
  image: golang:1.23
  restart: always
  working_dir: /go/src/app
  environment:
    - GOCACHE=/go/src/app/.cache/go-build
  env_file:
    - ./.env
  volumes:
    - ./src:/go/src/app
    - ./.env:/go/src/app/.env
    - ./config-example.yaml:/go/src/app/config.yaml
    - go-cache:/go/src/app/.cache/go-build
  command: "go run main.go"
  depends_on:
    lavinmq:
      condition: service_healthy

services:
  producer:
    <<: *golang-service
    working_dir: /go/src/app/producer
    ports:
      - "2000:2000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://127.0.0.1:2000/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 1s

  consumer:
    <<: *golang-service
    deploy:
      replicas: 1
    working_dir: /go/src/app/consumer
    command: "go run main.go --config /go/src/app/config.yaml"

  lavinmq:
    image: cloudamqp/lavinmq:latest
    expose:
      - "5672"
    ports:
      - "5672:5672"   # AMQP protocol port
      - "15672:15672" # Management UI port
    volumes:
      - ./lavinmq_data:/var/lib/lavinmq
    environment:
      - LAVINMQ_DEFAULT_USER=guest
      - LAVINMQ_DEFAULT_PASS=guest
    restart: always
    healthcheck:
      test: ["CMD", "lavinmqctl", "status"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

volumes:
  go-cache: {}