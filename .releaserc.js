module.exports = (() => {
    // Extract MR ID from GitLab CI environment variables
    const mrId = process.env.CI_MERGE_REQUEST_IID || 'dev';

    // Build your custom pre-release identifier
    const preReleaseId = `mr-${mrId}`;

    return {
        // Determine which branch we're on and set appropriate config
        branches: [
            'main', // Regular releases from main
            {
                name: process.env.CI_COMMIT_REF_NAME, // Current branch name
                prerelease: preReleaseId // Use our dynamic pre-release ID
            }
        ],
        plugins: [
            '@semantic-release/commit-analyzer',
            '@semantic-release/gitlab'
        ]
    };
})();