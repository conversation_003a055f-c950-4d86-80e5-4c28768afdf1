module.exports = (() => {
    // Extract MR ID from GitLab CI environment variables
    const mrId = process.env.CI_MERGE_REQUEST_IID || 'dev';
    const currentBranch = process.env.CI_COMMIT_REF_NAME;

    // Build your custom pre-release identifier
    const preReleaseId = `mr-${mrId}`;

    return {
        // Determine which branch we're on and set appropriate config
        branches: [
            'main', // Regular releases from main
            // Only add prerelease branch if we're not on main
            ...(currentBranch && currentBranch !== 'main' ? [{
                name: currentBranch,
                prerelease: preReleaseId
            }] : [])
        ],
        plugins: [
            '@semantic-release/commit-analyzer',
            '@semantic-release/release-notes-generator',
            '@semantic-release/gitlab',
            '@semantic-release/git'
        ]
    };
})();