package main

import (
	"context"
	"errors"
	"flag"
	"log"
	"log/slog"
	"os"
	"os/signal"
	"syscall"
	"time"

	json "github.com/bytedance/sonic"
	"github.com/gofiber/fiber/v3"
	"github.com/gofiber/fiber/v3/middleware/logger"
	"github.com/gofiber/fiber/v3/middleware/recover"

	"queue-manager/common/config"
	"queue-manager/common/default_connection"
	"queue-manager/common/rabbitmq/producer"
	"queue-manager/producer/handlers"
)

func main() {
	config.Load()

	slog.SetLogLoggerLevel(config.GetLogLevel())

	// Parse command-line flags
	httpAddress := flag.String("addr", ":2000", "HTTP server address")
	mainExchangeName := flag.String("exchange", default_connection.MainExchangeName, "Main exchange name")
	flag.Parse()

	log.Println("Starting queue manager producer service")

	// Create producer service - this will now set up all exchanges and queues with proper bindings
	log.Println("Initializing dispatcher service and setting up queues with bindings...")
	conn, err := default_connection.GetConnection("producer")
	if err != nil {
		log.Fatalf("Failed to create connection: %v", err)
	}
	defer conn.Close()

	mainProducer, err := producer.NewProducer(conn, *mainExchangeName)
	if err != nil {
		log.Fatalf("Failed to create dispatcher service: %v", err)
	}
	defer mainProducer.Close()

	// Create HTTP server with Fiber
	app := fiber.New(fiber.Config{
		ErrorHandler: func(c fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			var e *fiber.Error
			if errors.As(err, &e) {
				code = e.Code
			}

			// Set Content-Topic: application/json
			c.Set(fiber.HeaderContentType, fiber.MIMEApplicationJSON)

			return c.Status(code).JSON(fiber.Map{
				"error": err.Error(),
			})
		},
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
		JSONEncoder:  json.Marshal,
		JSONDecoder:  json.Unmarshal,
	})

	// Middleware
	app.Use(recover.New())
	app.Use(logger.New())

	// Register routes
	dispatchHandler := handlers.NewDispatchHandler(mainProducer)
	app.Post("/dispatch", dispatchHandler.HandleDispatch)

	// Health endpoint
	app.Get("/health", func(c fiber.Ctx) error {
		return c.Status(200).SendString("OK")
	})

	// Start HTTP server in a goroutine
	go func() {
		log.Printf("Starting HTTP server on %s", *httpAddress)
		if err := app.Listen(*httpAddress); err != nil {
			log.Fatalf("Failed to start HTTP server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shut down the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	signal := <-quit
	log.Printf("Received signal %s, shutting down gracefully...", signal)

	// Create a context with timeout for graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	// Close the HTTP server
	if err := app.ShutdownWithContext(ctx); err != nil {
		log.Printf("Error shutting down HTTP server: %v", err)
	} else {
		log.Println("HTTP server shut down successfully")
	}

	log.Println("Server exited")
}
