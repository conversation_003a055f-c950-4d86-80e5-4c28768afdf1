package handlers

import (
	"bytes"
	"io"
	"net/http/httptest"
	"testing"

	"github.com/gofiber/fiber/v3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"queue-manager/common/queue"
)

// MockProducer mocks the queue.Producer interface for testing
type MockProducer struct {
	mock.Mock
}

func (m *MockProducer) Publish(topic string, payload []byte) error {
	args := m.Called(topic, payload)
	return args.Error(0)
}

func (m *MockProducer) PublishWithHashKey(topic string, hashKey string, payload []byte) error {
	args := m.Called(topic, hashKey, payload)
	return args.Error(0)
}

func (m *MockProducer) Close() error {
	args := m.Called()
	return args.Error(0)
}

func setupApp(producer queue.Producer) *fiber.App {
	app := fiber.New()
	handler := NewDispatchHandler(producer)
	app.Post("/dispatch", handler.HandleDispatch)
	return app
}

func TestHandleDispatchSuccess(t *testing.T) {
	// Create mock producer
	mockProducer := new(MockProducer)

	// Setup expectations
	mockProducer.On("PublishWithHashKey", "message.created", "user123", mock.Anything).Return(nil)

	// Setup app with mock producer
	app := setupApp(mockProducer)

	// Create test request
	payload := `{
		"topic": "message.created",
		"hashKey": "user123",
		"payload": {
			"id": "msg456",
			"content": "Hello, world!"
		}
	}`

	req := httptest.NewRequest("POST", "/dispatch", bytes.NewBufferString(payload))
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	resp, err := app.Test(req)
	assert.NoError(t, err)

	// Check response
	assert.Equal(t, 200, resp.StatusCode)

	// Verify mock expectations
	mockProducer.AssertExpectations(t)
}

func TestHandleDispatchMissingTopic(t *testing.T) {
	// Create mock producer
	mockProducer := new(MockProducer)

	// Setup app with mock producer
	app := setupApp(mockProducer)

	// Create test request with missing topic
	payload := `{
		"hashKey": "user123",
		"payload": {
			"id": "msg456",
			"content": "Hello, world!"
		}
	}`

	req := httptest.NewRequest("POST", "/dispatch", bytes.NewBufferString(payload))
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	resp, err := app.Test(req)
	assert.NoError(t, err)

	// Check response
	assert.Equal(t, 400, resp.StatusCode)

	// Read response body
	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)
	assert.Contains(t, string(body), "Topic is required")
}

func TestHandleDispatchMissingPayload(t *testing.T) {
	// Create mock producer
	mockProducer := new(MockProducer)

	// We don't expect any calls to the producer because validation should fail
	// But we need to set up the mock to handle the call if validation doesn't work
	mockProducer.On("PublishWithHashKey", mock.Anything, mock.Anything, mock.Anything).Return(nil)

	// Setup app with mock producer
	app := setupApp(mockProducer)

	// Create test request with null payload
	payload := `{
		"topic": "message.created",
		"hashKey": "user123",
		"payload": null
	}`

	req := httptest.NewRequest("POST", "/dispatch", bytes.NewBufferString(payload))
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	resp, err := app.Test(req)
	assert.NoError(t, err)

	// Check response
	assert.Equal(t, 400, resp.StatusCode)

	// Read response body
	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)
	assert.Contains(t, string(body), "Payload is required")
}

func TestHandleDispatchInvalidJSON(t *testing.T) {
	// Create mock producer
	mockProducer := new(MockProducer)

	// Setup app with mock producer
	app := setupApp(mockProducer)

	// Create test request with invalid JSON
	payload := `{
		"topic": "message.created",
		"hashKey": "user123",
		"payload": {
			"id": "msg456",
			"content": "Hello, world!"
		}
	` // Missing closing brace

	req := httptest.NewRequest("POST", "/dispatch", bytes.NewBufferString(payload))
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	resp, err := app.Test(req)
	assert.NoError(t, err)

	// Check response
	assert.Equal(t, 400, resp.StatusCode)

	// Read response body
	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)
	assert.Contains(t, string(body), "Invalid request body")
}

func TestHandleDispatchPublishError(t *testing.T) {
	// Create mock producer
	mockProducer := new(MockProducer)

	// Setup expectations for error case
	mockProducer.On("PublishWithHashKey", "message.created", "user123", mock.Anything).Return(assert.AnError)

	// Setup app with mock producer
	app := setupApp(mockProducer)

	// Create test request
	payload := `{
		"topic": "message.created",
		"hashKey": "user123",
		"payload": {
			"id": "msg456",
			"content": "Hello, world!"
		}
	}`

	req := httptest.NewRequest("POST", "/dispatch", bytes.NewBufferString(payload))
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	resp, err := app.Test(req)
	assert.NoError(t, err)

	// Check response
	assert.Equal(t, 500, resp.StatusCode)

	// Read response body
	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)
	assert.Contains(t, string(body), "Failed to dispatch job")

	// Verify mock expectations
	mockProducer.AssertExpectations(t)
}
