package handlers

import (
	"log/slog"

	json "github.com/bytedance/sonic"
	"github.com/gofiber/fiber/v3"

	"queue-manager/common/queue"
)

// DispatchHandler handles dispatch HTTP requests
type DispatchHandler struct {
	producer queue.Producer
	logger   *slog.Logger
}

// NewDispatchHandler creates a new dispatch handler
func NewDispatchHandler(producer queue.Producer) *DispatchHandler {
	return &DispatchHandler{
		producer: producer,
		logger:   slog.With("component", "dispatch-handler"),
	}
}

// HandleDispatch handles POST /dispatch requests
func (h *DispatchHandler) HandleDispatch(c fiber.Ctx) error {
	body := c.Body()

	// Parse request body
	var req queue.Message
	if err := json.Unmarshal(body, &req); err != nil {
		h.logger.Error("Failed to parse request body", "error", err)
		return fiber.NewError(fiber.StatusBadRequest, "Invalid request body")
	}

	// Validate request
	if req.Topic == "" {
		return fiber.NewError(fiber.StatusBadRequest, "Topic is required")
	}

	// Check for empty or null payload
	if len(req.Payload) == 0 || string(req.Payload) == "null" || string(req.Payload) == "{}" {
		return fiber.NewError(fiber.StatusBadRequest, "Payload is required")
	}

	// Log the incoming request
	h.logger.Info("Received dispatch request",
		"type", req.Topic,
		"hashKey", req.HashKey,
		"payloadSize", len(req.Payload))

	// Dispatch the job
	err := h.producer.PublishWithHashKey(req.Topic, req.HashKey, body)
	if err != nil {
		h.logger.Error("Failed to dispatch job", "error", err)
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to dispatch job")
	}

	// Return success response
	return c.Status(200).Send(nil)
}
