package integration

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gofiber/fiber/v3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"queue-manager/common/queue"
	"queue-manager/producer/handlers"
)

// MockProducer mocks the queue.Producer interface for testing
type MockProducer struct {
	mock.Mock
}

func (m *MockProducer) Publish(topic string, payload []byte) error {
	args := m.Called(topic, payload)
	return args.Error(0)
}

func (m *MockProducer) PublishWithHashKey(topic string, hashKey string, payload []byte) error {
	args := m.Called(topic, hashKey, payload)
	return args.Error(0)
}

func (m *MockProducer) Close() error {
	args := m.Called()
	return args.Error(0)
}

func setupAPIApp(producer queue.Producer) *fiber.App {
	app := fiber.New()

	// Register routes
	dispatchHandler := handlers.NewDispatchHandler(producer)
	app.Post("/dispatch", dispatchHandler.HandleDispatch)

	// Health endpoint
	app.Get("/health", func(c fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status": "ok",
		})
	})

	return app
}

func TestHealthEndpoint(t *testing.T) {
	// Create mock producer
	mockProducer := new(MockProducer)

	// Setup app
	app := setupAPIApp(mockProducer)

	// Create test request
	req := httptest.NewRequest("GET", "/health", nil)

	// Perform request
	resp, err := app.Test(req)
	require.NoError(t, err)

	// Check response
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	// Parse response body
	var result map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err)

	// Verify response content
	assert.Equal(t, "ok", result["status"])
}

func TestDispatchEndpoint(t *testing.T) {
	// Create mock producer
	mockProducer := new(MockProducer)

	// Setup expectations
	mockProducer.On("PublishWithHashKey", "message.created", "user123", mock.Anything).Return(nil)

	// Setup app
	app := setupAPIApp(mockProducer)

	// Create test message
	message := map[string]interface{}{
		"topic":   "message.created",
		"hashKey": "user123",
		"payload": map[string]interface{}{
			"id":      "msg456",
			"content": "Hello, world!",
		},
	}

	// Serialize message
	messageBytes, err := json.Marshal(message)
	require.NoError(t, err)

	// Create test request
	req := httptest.NewRequest("POST", "/dispatch", bytes.NewBuffer(messageBytes))
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	resp, err := app.Test(req)
	require.NoError(t, err)

	// Check response
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	// Verify mock expectations
	mockProducer.AssertExpectations(t)
}

func TestDispatchEndpointValidation(t *testing.T) {
	// Create mock producer
	mockProducer := new(MockProducer)

	// Setup app
	app := setupAPIApp(mockProducer)

	// Test cases
	testCases := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		expectedError  string
	}{
		{
			name: "Missing topic",
			requestBody: map[string]interface{}{
				"hashKey": "user123",
				"payload": map[string]interface{}{
					"id": "msg456",
				},
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Topic is required",
		},
		{
			name: "Empty payload",
			requestBody: map[string]interface{}{
				"topic":   "message.created",
				"hashKey": "user123",
				"payload": nil,
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Payload is required",
		},
		{
			name:           "Invalid JSON",
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid request body",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Serialize request body
			var body []byte
			var err error

			if str, ok := tc.requestBody.(string); ok {
				body = []byte(str)
			} else {
				body, err = json.Marshal(tc.requestBody)
				require.NoError(t, err)
			}

			// Create test request
			req := httptest.NewRequest("POST", "/dispatch", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// Perform request
			resp, err := app.Test(req)
			require.NoError(t, err)

			// Check response
			assert.Equal(t, tc.expectedStatus, resp.StatusCode)

			// Read response body
			respBody, err := io.ReadAll(resp.Body)
			require.NoError(t, err)

			// Verify error message
			assert.Contains(t, string(respBody), tc.expectedError)
		})
	}
}
