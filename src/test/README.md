# Queue Manager Tests

This directory contains tests for the Queue Manager project.

## Test Structure

The tests are organized as follows:

```
test/
├── config/           # Test configuration files
├── helpers/          # Test helper functions
└── integration/      # Integration tests
```

## Running Tests

### Unit Tests

Unit tests can be run with:

```bash
make test-unit
```

or simply:

```bash
make test
```

### Integration Tests

Integration tests require Docker to be running, as they use `dockertest` to spin up a RabbitMQ container:

```bash
make test-integration
```

### All Tests

To run both unit and integration tests:

```bash
make test-all
```

## Test Coverage

Generate a test coverage report with:

```bash
make coverage
```

This will create a `coverage.html` file that you can open in your browser.

## Test Configuration

The integration tests use a test configuration file located at `test/config/test-config.yaml`. This file defines test queues and workers for integration testing.

## Test Helpers

The `helpers` package provides utilities for integration testing, including:

- `SetupRabbitMQ`: Creates a RabbitMQ container for testing
- `TeardownRabbitMQ`: Stops and removes the RabbitMQ container
- `CreateConnection`: Creates a connection to the RabbitMQ container
- `PublishMessage`: Publishes a message to the RabbitMQ container
- `ConsumeMessages`: Consumes messages from the RabbitMQ container

## Mock Objects

The tests use mock implementations of the core interfaces:

- `MockProducer`: Implements the `queue.Producer` interface
- `MockConsumer`: Implements the `queue.Consumer` interface
- `MockConsumerGroup`: Implements the `queue.ConsumerGroup` interface
- `MockRetryPolicy`: Implements the `queue.RetryPolicy` interface
- `MockChannel`: Mocks the `amqp.Channel` for testing
- `MockChannelPool`: Mocks the `channel_pool.ChannelPool` for testing
- `MockWorker`: Implements the `worker.Worker` interface
- `MockConsumerFactory`: Mocks the consumer factory for testing

## Integration Tests

The integration tests cover:

1. **Producer-Consumer Integration**: Tests message flow from producer to consumer
2. **Partitioned Queue Routing**: Tests consistent hash-based routing
3. **Retry Policy**: Tests message retries and dead-letter queue functionality
4. **HTTP API**: Tests the producer API endpoints

## Skipping Integration Tests

Integration tests can be skipped in CI environments by setting the `SKIP_INTEGRATION_TESTS` environment variable:

```bash
SKIP_INTEGRATION_TESTS=true make test-all
```

Or by using the `-short` flag with `go test`:

```bash
go test -short ./...
```
