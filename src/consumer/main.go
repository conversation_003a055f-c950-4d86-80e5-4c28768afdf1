package main

import (
	"flag"
	"log"
	"log/slog"
	"os"
	"os/signal"
	"syscall"

	"queue-manager/common/config"
	"queue-manager/common/default_connection"
	"queue-manager/common/queue_config"
	"queue-manager/consumer/service"
	"queue-manager/consumer/worker"
)

func main() {
	config.Load()

	slog.SetLogLoggerLevel(config.GetLogLevel())

	// Parse command-line flags
	configPath := flag.String("config", "config.yaml", "Path to configuration file")
	flag.Parse()

	// Load configuration
	cfg, err := queue_config.ParseConfigFromFile(*configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	conn, err := default_connection.GetConnection("consumer")
	if err != nil {
		log.Fatalf("Failed to create connection: %v", err)
	}
	defer conn.Close()

	// Create the HTTP worker
	httpWorker := worker.NewHTTPWorker(cfg)

	// Create the job processor service
	processor := service.NewJobProcessor(conn, cfg, httpWorker)
	// Start processing all queues
	if err := processor.StartProcessing(); err != nil {
		log.Fatalf("Failed to start job processing: %v", err)
	}
	defer processor.Close()

	log.Println("Consumer started. Press Ctrl+C to exit")

	// Wait for interrupt signal to gracefully shut down
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down gracefully...")
}
