package service

import (
	"context"
	"encoding/json"
	"log/slog"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"queue-manager/common/queue"
)

// MockWorker mocks the worker interface for testing
type MockWorker struct {
	mock.Mock
}

// Worker defines the interface for processing messages
type Worker interface {
	ProcessMessage(ctx context.Context, queueName string, message *queue.Message, rawMessage []byte) error
}

// Ensure MockWorker implements the Worker interface
var _ Worker = (*MockWorker)(nil)

func (m *MockWorker) ProcessMessage(ctx context.Context, queueName string, message *queue.Message, rawMessage []byte) error {
	args := m.Called(ctx, queueName, message, rawMessage)
	return args.Error(0)
}

// MockConsumerGroup mocks the queue.ConsumerGroup interface for testing
type MockConsumerGroup struct {
	mock.Mock
}

func (m *MockConsumerGroup) Start() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockConsumerGroup) Stop() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockConsumerGroup) IsRunning() bool {
	args := m.Called()
	return args.Bool(0)
}

// MockConsumerFactory is a placeholder for the real implementation
type MockConsumerFactory struct{}

// TestJobProcessor is a simplified version of JobProcessor for testing
type TestJobProcessor struct {
	worker         Worker
	logger         *slog.Logger
	consumerGroups []queue.ConsumerGroup
}

// createMessageHandler creates a message handler function for the specified queue
func (p *TestJobProcessor) createMessageHandler(queueName string) queue.MessageHandler {
	return func(ctx context.Context, body []byte) error {
		// Parse the message
		var message queue.Message
		if err := json.Unmarshal(body, &message); err != nil {
			return err
		}

		// Process the message using the worker
		return p.worker.ProcessMessage(ctx, queueName, &message, body)
	}
}

// StopConsumers stops all consumer groups
func (p *TestJobProcessor) StopConsumers() {
	for _, group := range p.consumerGroups {
		if err := group.Stop(); err != nil {
			p.logger.Error("Error stopping consumer group", "error", err)
		}
	}
}

func TestCreateMessageHandler(t *testing.T) {
	// Create mock worker
	mockWorker := new(MockWorker)

	// Create a test-specific job processor
	processor := &TestJobProcessor{
		worker: mockWorker,
		logger: slog.Default(),
	}

	// Create message handler
	handler := processor.createMessageHandler("test-queue")
	assert.NotNil(t, handler)

	// Create test message
	message := &queue.Message{
		Topic:   "message.created",
		HashKey: "user123",
		Payload: json.RawMessage(`{"id":"msg456","content":"Hello, world!"}`),
	}

	// Serialize message
	rawMessage, err := json.Marshal(message)
	require.NoError(t, err)

	// Setup expectations
	mockWorker.On("ProcessMessage", mock.Anything, "test-queue", mock.Anything, rawMessage).Return(nil)

	// Call handler
	err = handler(context.Background(), rawMessage)
	assert.NoError(t, err)

	// Verify mock expectations
	mockWorker.AssertExpectations(t)
}

func TestCreateMessageHandlerInvalidJSON(t *testing.T) {
	// Create mock worker
	mockWorker := new(MockWorker)

	// Create a test-specific job processor
	processor := &TestJobProcessor{
		worker: mockWorker,
		logger: slog.Default(),
	}

	// Create message handler
	handler := processor.createMessageHandler("test-queue")
	assert.NotNil(t, handler)

	// Call handler with invalid JSON
	err := handler(context.Background(), []byte(`{invalid json`))
	assert.Error(t, err)

	// Verify mock expectations - worker should not be called
	mockWorker.AssertNotCalled(t, "ProcessMessage")
}

func TestCreateMessageHandlerWorkerError(t *testing.T) {
	// Create mock worker
	mockWorker := new(MockWorker)

	// Create a test-specific job processor
	processor := &TestJobProcessor{
		worker: mockWorker,
		logger: slog.Default(),
	}

	// Create message handler
	handler := processor.createMessageHandler("test-queue")
	assert.NotNil(t, handler)

	// Create test message
	message := &queue.Message{
		Topic:   "message.created",
		HashKey: "user123",
		Payload: json.RawMessage(`{"id":"msg456","content":"Hello, world!"}`),
	}

	// Serialize message
	rawMessage, err := json.Marshal(message)
	require.NoError(t, err)

	// Setup expectations for error case
	mockWorker.On("ProcessMessage", mock.Anything, "test-queue", mock.Anything, rawMessage).Return(assert.AnError)

	// Call handler
	err = handler(context.Background(), rawMessage)
	assert.Error(t, err)

	// Verify mock expectations
	mockWorker.AssertExpectations(t)
}

func TestStopConsumers(t *testing.T) {
	// Create mock consumer groups
	mockConsumerGroup1 := new(MockConsumerGroup)
	mockConsumerGroup2 := new(MockConsumerGroup)

	// Setup expectations
	mockConsumerGroup1.On("Stop").Return(nil)
	mockConsumerGroup2.On("Stop").Return(nil)

	// Create a test-specific job processor with consumer groups
	processor := &TestJobProcessor{
		worker:         nil, // Not needed for this test
		logger:         slog.Default(),
		consumerGroups: []queue.ConsumerGroup{mockConsumerGroup1, mockConsumerGroup2},
	}

	// Stop consumers
	processor.StopConsumers()

	// Verify mock expectations
	mockConsumerGroup1.AssertExpectations(t)
	mockConsumerGroup2.AssertExpectations(t)
}
