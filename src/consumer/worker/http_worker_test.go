package worker

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"queue-manager/common/queue"
)

// MockConfig implements the interface needed for the HTTP worker
type MockConfig struct {
	mock.Mock
}

// Ensure MockConfig implements the same interface as queue_config.Config
var _ interface {
	GetQueueURL(string) (string, error)
} = (*MockConfig)(nil)

func (m *MockConfig) GetQueueURL(queueName string) (string, error) {
	args := m.Called(queueName)
	return args.String(0), args.Error(1)
}

func TestProcessMessage(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify request method
		assert.Equal(t, "POST", r.Method)

		// Verify content type
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))

		// Parse request body
		var message queue.Message
		err := json.NewDecoder(r.Body).Decode(&message)
		assert.NoError(t, err)

		// Verify message content
		assert.Equal(t, "message.created", message.Topic)
		assert.Equal(t, "user123", message.HashKey)

		// Send response
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	// Create mock config
	mockConfig := new(MockConfig)
	mockConfig.On("GetQueueURL", "test-queue").Return(server.URL, nil)

	// Create HTTP worker with custom timeout
	worker := NewHTTPWorker(mockConfig)

	// Create test message
	message := &queue.Message{
		Topic:   "message.created",
		HashKey: "user123",
		Payload: json.RawMessage(`{"id":"msg456","content":"Hello, world!"}`),
	}

	// Serialize message
	rawMessage, err := json.Marshal(message)
	require.NoError(t, err)

	// Process message
	err = worker.ProcessMessage(context.Background(), "test-queue", message, rawMessage)
	assert.NoError(t, err)

	// Verify mock expectations
	mockConfig.AssertExpectations(t)
}

func TestProcessMessageServerError(t *testing.T) {
	// Create a test server that returns an error
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		if _, err := w.Write([]byte("Internal server error")); err != nil {
			t.Logf("Error writing response: %v", err)
		}
	}))
	defer server.Close()

	// Create mock config
	mockConfig := new(MockConfig)
	mockConfig.On("GetQueueURL", "test-queue").Return(server.URL, nil)

	// Create HTTP worker
	worker := NewHTTPWorker(mockConfig)

	// Create test message
	message := &queue.Message{
		Topic:   "message.created",
		HashKey: "user123",
		Payload: json.RawMessage(`{"id":"msg456","content":"Hello, world!"}`),
	}

	// Serialize message
	rawMessage, err := json.Marshal(message)
	require.NoError(t, err)

	// Process message
	err = worker.ProcessMessage(context.Background(), "test-queue", message, rawMessage)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "worker returned status code 500")

	// Verify mock expectations
	mockConfig.AssertExpectations(t)
}

func TestProcessMessageTimeout(t *testing.T) {
	// Create a test server that times out
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Sleep longer than the timeout
		time.Sleep(200 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	// Create mock config
	mockConfig := new(MockConfig)
	mockConfig.On("GetQueueURL", "test-queue").Return(server.URL, nil)

	// Create HTTP worker with very short timeout
	worker := &HTTPWorker{
		config: mockConfig,
		client: &http.Client{
			Timeout: 100 * time.Millisecond,
		},
		logger: slog.Default(),
	}

	// Create test message
	message := &queue.Message{
		Topic:   "message.created",
		HashKey: "user123",
		Payload: json.RawMessage(`{"id":"msg456","content":"Hello, world!"}`),
	}

	// Serialize message
	rawMessage, err := json.Marshal(message)
	require.NoError(t, err)

	// Process message
	err = worker.ProcessMessage(context.Background(), "test-queue", message, rawMessage)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "context deadline exceeded")

	// Verify mock expectations
	mockConfig.AssertExpectations(t)
}

func TestProcessMessageConfigError(t *testing.T) {
	// Create mock config that returns an error
	mockConfig := new(MockConfig)
	mockConfig.On("GetQueueURL", "test-queue").Return("", assert.AnError)

	// Create HTTP worker
	worker := NewHTTPWorker(mockConfig)

	// Create test message
	message := &queue.Message{
		Topic:   "message.created",
		HashKey: "user123",
		Payload: json.RawMessage(`{"id":"msg456","content":"Hello, world!"}`),
	}

	// Serialize message
	rawMessage, err := json.Marshal(message)
	require.NoError(t, err)

	// Process message
	err = worker.ProcessMessage(context.Background(), "test-queue", message, rawMessage)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to get worker URL")

	// Verify mock expectations
	mockConfig.AssertExpectations(t)
}

func TestNewHTTPWorker(t *testing.T) {
	// Remove the skip
	// Create a mock config
	mockConfig := new(MockConfig)

	// Create HTTP worker
	worker := NewHTTPWorker(mockConfig)

	// Verify worker properties
	assert.NotNil(t, worker)
	assert.Equal(t, mockConfig, worker.config)
	assert.NotNil(t, worker.client)
	assert.Equal(t, 60*time.Second, worker.client.Timeout)
}
