package worker

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"time"

	"queue-manager/common/queue"
)

// QueueURLGetter is an interface for getting queue URLs
type QueueURLGetter interface {
	GetQueueURL(queueName string) (string, error)
}

// HTTPWorker is responsible for sending jobs to worker endpoints
type HTTPWorker struct {
	config QueueURLGetter
	client *http.Client
	logger *slog.Logger
}

// NewHTTPWorker creates a new HTTP worker
func NewHTTPWorker(cfg QueueURLGetter) *HTTPWorker {
	transport := &http.Transport{
		MaxIdleConns:        500,             // Maximum idle connections across all hosts
		MaxIdleConnsPerHost: 100,             // Maximum idle connections per host
		IdleConnTimeout:     1 * time.Minute, // How long to keep idle connections alive
	}

	// Create an HTTP client with the specified timeout
	client := &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second,
	}

	return &HTTPWorker{
		config: cfg,
		client: client,
		logger: slog.With("component", "http-worker"),
	}
}

// ProcessMessage sends a job to the appropriate worker endpoint
func (w *HTTPWorker) ProcessMessage(ctx context.Context, queueName string, message *queue.Message, rawMessage []byte) error {
	url, err := w.config.GetQueueURL(queueName)
	if err != nil {
		return fmt.Errorf("failed to get worker URL: %w", err)
	}

	logger := w.logger.With("topic", message.Topic,
		"queue", queueName,
		"url", url)

	// Create a context with timeout
	ctx, cancel := context.WithTimeout(ctx, w.client.Timeout-time.Second)
	defer cancel()

	// Create the HTTP request
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(rawMessage))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Queue-Name", queueName)
	req.Header.Set("X-Topic", message.Topic)

	// Log the request
	logger.Debug("Sending message to worker")

	// Send the request
	resp, err := w.client.Do(req)
	if err != nil {
		logger.Error("HTTP request failed",
			"error", err)
		return fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read the response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("Failed to read response body",
			"error", err)
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// Check for non-success status code
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		logger.Error("Handler returned non-success status code",
			"statusCode", resp.StatusCode,
			"response", string(respBody))
		return fmt.Errorf("worker returned status code %d: %s", resp.StatusCode, string(respBody))
	}

	logger.Debug("Handler processed message successfully",
		"statusCode", resp.StatusCode)

	return nil
}

// Close releases resources
func (w *HTTPWorker) Close() error {
	// Close any idle connections
	w.client.CloseIdleConnections()
	return nil
}
