# Options for analysis running
run:
  # Timeout for analysis, e.g. 30s, 5m, default is 1m
  timeout: 5m

  # Include test files or not, default is true
  tests: true

  # Exclude directories from analysis
  exclude-dirs:
    - vendor
    - build

  # Select the Go version to target
  go: "1.23"

# Using default output configuration (colored-line-number format)

# All available settings of specific linters
linters-settings:
  errcheck:
    # Report about not checking of errors in type assertions: `a := b.(MyStruct)`;
    # default is false: such cases aren't reported by default.
    check-type-assertions: true

    # Report about assignment of errors to blank identifier: `num, _ := strconv.Atoi(numStr)`;
    # default is false: such cases aren't reported by default.
    check-blank: true

  govet:
    # Enable all analyzers
    enable-all: true

  gocyclo:
    # Minimal code complexity to report, 30 by default
    min-complexity: 15

  dupl:
    # Tokens count to trigger issue, 150 by default
    threshold: 100

  goconst:
    # Minimal length of string constant, 3 by default
    min-len: 3
    # Minimal occurrences count to trigger, 3 by default
    min-occurrences: 3

  # Settings for all linters

# Enable or disable specific linters
linters:
  disable-all: true
  enable:
    - errcheck      # Detect unchecked errors
    - gosimple      # Simplify code
    - ineffassign   # Detect when assignments to existing variables are not used
    - staticcheck   # Go static analysis
    - typecheck     # Like the front-end of a Go compiler
    - unused        # Check for unused constants, variables, functions and types

# Issues configuration
issues:
  # Maximum issues count per one linter. Set to 0 to disable. Default is 50.
  max-issues-per-linter: 0

  # Maximum count of issues with the same text. Set to 0 to disable. Default is 3.
  max-same-issues: 0

  # Exit code when issues were found, default is 1
  exit-code: 0

  # Excluding configuration
  exclude-rules:
    # Exclude some linters from running on tests files.
    - path: _test\.go
      linters:
        - gocyclo
        - dupl
        - gosec

    # Exclude known linters from partially hard-vendored code,
    # which is impossible to exclude via "nolint" comments.
    - path: internal/hmac/
      text: "weak cryptographic primitive"
      linters:
        - gosec

    # Exclude some staticcheck messages
    - linters:
        - staticcheck
      text: "SA1019: channel.QueueInspect is deprecated"
