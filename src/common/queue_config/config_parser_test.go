package queue_config

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseConfigFromFile(t *testing.T) {
	// Create a temporary config file
	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "test-config.yaml")

	configContent := `
handlers:
  workers-node:
    url: http://workers:8080/run/:queueName
  workers-go:
    url: http://workers-go:8080/run/:queueName

defaults:
  max-retries: 5
  processing-timeout-seconds: 60
  mode: concurrent
  consumers-per-instance: 10
  handler: workers-node
  partitions: 3

queues:
  notification:
    mode: concurrent
    max-retries: 3
    consumers-per-instance: 10
    handler: workers-node
    topics:
      - 'message.created'

  user-events:
    mode: sequential
    partitions: 5
    max-retries: 3
    handler: workers-go
    topics:
      - 'user.created'
      - 'user.updated'
      - 'user.deleted'
      
  defaults-test:
    topics:
      - 'test.topic'
      
  sequential-defaults-test:
    mode: sequential
    topics:
      - 'sequential.topic'
`

	err := os.WriteFile(configPath, []byte(configContent), 0644)
	require.NoError(t, err, "Failed to write test config file")

	// Parse the config
	config, err := ParseConfigFromFile(configPath)
	require.NoError(t, err, "Failed to parse config file")

	// Verify handlers
	assert.Len(t, config.Handlers, 2, "Should have 2 handlers")
	assert.Equal(t, "http://workers:8080/run/:queueName", config.Handlers["workers-node"].URLPattern)
	assert.Equal(t, "http://workers-go:8080/run/:queueName", config.Handlers["workers-go"].URLPattern)

	// Verify defaults
	assert.Equal(t, 5, config.Defaults.MaxRetries)
	assert.Equal(t, int64(60), config.Defaults.ProcessingTimeoutSeconds)
	assert.Equal(t, "concurrent", config.Defaults.Mode)
	assert.Equal(t, 10, config.Defaults.ConsumersPerInstance)
	assert.Equal(t, "workers-node", config.Defaults.Handler)
	assert.Equal(t, 3, config.Defaults.Partitions)

	// Verify queues
	assert.Len(t, config.Queues, 4, "Should have 4 queues")

	// Check notification queue (explicitly defined values)
	notificationQueue, exists := config.Queues["notification"]
	assert.True(t, exists, "notification queue should exist")
	assert.Equal(t, "concurrent", notificationQueue.Mode)
	assert.Equal(t, 3, notificationQueue.MaxRetries)
	assert.Equal(t, 10, notificationQueue.ConsumersPerInstance)
	assert.Equal(t, "workers-node", notificationQueue.Handler)
	assert.Equal(t, []string{"message.created"}, notificationQueue.Topics)
	assert.Equal(t, int64(60), notificationQueue.ProcessingTimeoutSeconds, "Default timeout should be applied")
	assert.Equal(t, 0, notificationQueue.Partitions, "Partitions default should not be applied to concurrent mode")

	// Check user-events queue
	userEventsQueue, exists := config.Queues["user-events"]
	assert.True(t, exists, "user-events queue should exist")
	assert.Equal(t, "sequential", userEventsQueue.Mode)
	assert.Equal(t, 5, userEventsQueue.Partitions, "Explicit partitions should override default")
	assert.Equal(t, 3, userEventsQueue.MaxRetries)
	assert.Equal(t, "workers-go", userEventsQueue.Handler)
	assert.Equal(t, []string{"user.created", "user.updated", "user.deleted"}, userEventsQueue.Topics)
	assert.Equal(t, int64(60), userEventsQueue.ProcessingTimeoutSeconds, "Default timeout should be applied")

	// Check defaults-test queue (should inherit all defaults except topics)
	defaultsTestQueue, exists := config.Queues["defaults-test"]
	assert.True(t, exists, "defaults-test queue should exist")
	assert.Equal(t, "concurrent", defaultsTestQueue.Mode, "Should inherit default mode")
	assert.Equal(t, 5, defaultsTestQueue.MaxRetries, "Should inherit default max-retries")
	assert.Equal(t, 10, defaultsTestQueue.ConsumersPerInstance, "Should inherit default consumers-per-instance")
	assert.Equal(t, "workers-node", defaultsTestQueue.Handler, "Should inherit default handler")
	assert.Equal(t, []string{"test.topic"}, defaultsTestQueue.Topics)
	assert.Equal(t, int64(60), defaultsTestQueue.ProcessingTimeoutSeconds, "Should inherit default timeout")
	assert.Equal(t, 0, defaultsTestQueue.Partitions, "Partitions default should not be applied to concurrent mode")

	// Check sequential-defaults-test queue (should inherit partitions from defaults)
	sequentialDefaultsTestQueue, exists := config.Queues["sequential-defaults-test"]
	assert.True(t, exists, "sequential-defaults-test queue should exist")
	assert.Equal(t, "sequential", sequentialDefaultsTestQueue.Mode, "Should use specified sequential mode")
	assert.Equal(t, 3, sequentialDefaultsTestQueue.Partitions, "Should inherit default partitions")
	assert.Equal(t, 5, sequentialDefaultsTestQueue.MaxRetries, "Should inherit default max-retries")
	assert.Equal(t, "workers-node", sequentialDefaultsTestQueue.Handler, "Should inherit default handler")
	assert.Equal(t, []string{"sequential.topic"}, sequentialDefaultsTestQueue.Topics)
}

func TestParseConfigWithDefaultsErrors(t *testing.T) {
	// Test with a sequential queue that doesn't specify partitions
	tempDir := t.TempDir()
	invalidConfigPath := filepath.Join(tempDir, "invalid-defaults-config.yaml")

	invalidContent := `
handlers:
  workers-node:
    url: http://workers:8080/run/:queueName

defaults:
  mode: sequential
  handler: workers-node
  # No partitions specified in defaults

queues:
  bad-sequential:
    topics:
      - 'test.topic'
    # Queue inherits sequential mode but has no partitions specified
`

	err := os.WriteFile(invalidConfigPath, []byte(invalidContent), 0644)
	require.NoError(t, err, "Failed to write invalid config file")

	_, err = ParseConfigFromFile(invalidConfigPath)
	assert.Error(t, err, "Should error with sequential mode without partitions")
	assert.Contains(t, err.Error(), "with sequential mode must specify partitions > 0")
}

func TestParseConfigFromFileErrors(t *testing.T) {
	// Test with non-existent file
	_, err := ParseConfigFromFile("non-existent-file.yaml")
	assert.Error(t, err, "Should error with non-existent file")

	// Test with invalid YAML
	tempDir := t.TempDir()
	invalidConfigPath := filepath.Join(tempDir, "invalid-config.yaml")

	invalidContent := `
handlers:
  - this is not valid YAML
    url: http://workers:8080
`

	err = os.WriteFile(invalidConfigPath, []byte(invalidContent), 0644)
	require.NoError(t, err, "Failed to write invalid config file")

	_, err = ParseConfigFromFile(invalidConfigPath)
	assert.Error(t, err, "Should error with invalid YAML")
}

func TestGetQueueURL(t *testing.T) {
	// Create a config with handlers
	config := &Config{
		Handlers: map[string]*Handler{
			"workers-node": {
				URLPattern: "http://workers:8080/run/:queueName",
			},
			"workers-go": {
				URLPattern: "http://workers-go:8080/run/:queueName",
			},
		},
		Queues: map[string]*Queue{
			"notification": {
				Handler: "workers-node",
			},
			"user-events": {
				Handler: "workers-go",
			},
		},
	}

	// Test valid queue and worker
	url, err := config.GetQueueURL("notification")
	assert.NoError(t, err)
	assert.Equal(t, "http://workers:8080/run/notification", url)

	url, err = config.GetQueueURL("user-events")
	assert.NoError(t, err)
	assert.Equal(t, "http://workers-go:8080/run/user-events", url)

	// Test non-existent queue
	_, err = config.GetQueueURL("non-existent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found in configuration")

	// Test queue with non-existent handler
	config.Queues["invalid-worker"] = &Queue{Handler: "non-existent-worker"}
	_, err = config.GetQueueURL("invalid-worker")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found in configuration")
}
