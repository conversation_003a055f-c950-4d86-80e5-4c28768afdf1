package queue_config

import (
	"fmt"
	"gopkg.in/yaml.v3"
	"os"
	"strings"
)

// <PERSON><PERSON> represents a worker group configuration
type Handler struct {
	URLPattern string `yaml:"url"`
}

// QueueDefaults represents default settings for queues
type QueueDefaults struct {
	MaxRetries               int    `yaml:"max-retries,omitempty"`
	ProcessingTimeoutSeconds int64  `yaml:"processing-timeout-seconds,omitempty"`
	Mode                     string `yaml:"mode,omitempty"`
	ConsumersPerInstance     int    `yaml:"consumers-per-instance,omitempty"`
	Handler                  string `yaml:"handler,omitempty"`
	Partitions               int    `yaml:"partitions,omitempty"`
}

// Queue represents a queue configuration
type Queue struct {
	Mode                     string   `yaml:"mode,omitempty"`
	Partitions               int      `yaml:"partitions,omitempty"`
	MaxRetries               int      `yaml:"max-retries,omitempty"`
	ConsumersPerInstance     int      `yaml:"consumers-per-instance,omitempty"`
	ProcessingTimeoutSeconds int64    `yaml:"processing-timeout-seconds,omitempty"`
	Handler                  string   `yaml:"handler"`
	Topics                   []string `yaml:"topics"`
}

// Config represents the main configuration structure
type Config struct {
	Handlers map[string]*Handler `yaml:"handlers"`
	Defaults QueueDefaults       `yaml:"defaults"`
	Queues   map[string]*Queue   `yaml:"queues"`
}

// ParseConfigFromFile reads and parses a worker queue configuration file from the given path
func ParseConfigFromFile(filePath string) (*Config, error) {
	// Read the file
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %w", filePath, err)
	}

	// Parse YAML data
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse YAML in %s: %w", filePath, err)
	}

	// Apply defaults and validate configuration
	if err := applyDefaultsAndValidate(&config); err != nil {
		return nil, fmt.Errorf("invalid configuration in %s: %w", filePath, err)
	}

	return &config, nil
}

// applyDefaultsAndValidate applies default values and performs validation on the parsed configuration
func applyDefaultsAndValidate(config *Config) error {
	// Apply defaults to each queue and validate
	for queueName, queue := range config.Queues {
		// Apply defaults if values are not specified
		if queue.MaxRetries == 0 && config.Defaults.MaxRetries != 0 {
			queue.MaxRetries = config.Defaults.MaxRetries
		}

		if queue.ProcessingTimeoutSeconds == 0 && config.Defaults.ProcessingTimeoutSeconds != 0 {
			queue.ProcessingTimeoutSeconds = config.Defaults.ProcessingTimeoutSeconds
		} else if queue.ProcessingTimeoutSeconds == 0 {
			// Apply hardcoded default if neither queue nor defaults specify
			queue.ProcessingTimeoutSeconds = 60
		}

		if queue.Mode == "" && config.Defaults.Mode != "" {
			queue.Mode = config.Defaults.Mode
		}

		if queue.ConsumersPerInstance == 0 && config.Defaults.ConsumersPerInstance != 0 {
			queue.ConsumersPerInstance = config.Defaults.ConsumersPerInstance
		}

		// Only apply partitions defaults for sequential mode
		if queue.Partitions == 0 && config.Defaults.Partitions != 0 &&
			(queue.Mode == "sequential" || (queue.Mode == "" && config.Defaults.Mode == "sequential")) {
			queue.Partitions = config.Defaults.Partitions
		}

		if queue.Handler == "" && config.Defaults.Handler != "" {
			queue.Handler = config.Defaults.Handler
		}

		// Validate that the handler exists
		if _, exists := config.Handlers[queue.Handler]; !exists {
			return fmt.Errorf("queue '%s' references non-existent worker group '%s'",
				queueName, queue.Handler)
		}

		// Validate mode
		if queue.Mode != "" && queue.Mode != "sequential" && queue.Mode != "concurrent" {
			return fmt.Errorf("queue '%s' has invalid mode '%s' (must be 'sequential' or 'concurrent')",
				queueName, queue.Mode)
		}

		// If mode is sequential, partitions must be specified
		if queue.Mode == "sequential" && queue.Partitions <= 0 {
			return fmt.Errorf("queue '%s' with sequential mode must specify partitions > 0", queueName)
		}

		// If mode is concurrent, consumers per instance must be specified
		if queue.Mode == "concurrent" && queue.ConsumersPerInstance <= 0 {
			return fmt.Errorf("queue '%s' with concurrent mode must specify consumers-per-instance > 0", queueName)
		}

		// Validate max retries
		if queue.MaxRetries < 0 {
			return fmt.Errorf("queue '%s' must specify max-retries >= 0", queueName)
		}

		// Validate processing timeout
		if queue.ProcessingTimeoutSeconds < 0 {
			return fmt.Errorf("queue '%s' must specify processing-timeout-seconds >= 0", queueName)
		}
	}

	return nil
}

// GetQueueURL returns the worker URL for the specified queue and job name
func (c *Config) GetQueueURL(queueName string) (string, error) {
	queue, exists := c.Queues[queueName]
	if !exists {
		return "", fmt.Errorf("queue '%s' not found in configuration", queueName)
	}

	workerGroup, exists := c.Handlers[queue.Handler]
	if !exists {
		return "", fmt.Errorf("worker group '%s' not found in configuration", queue.Handler)
	}

	// Replace :queueName placeholder with the actual queue name
	url := strings.Replace(workerGroup.URLPattern, ":queueName", queueName, -1)

	return url, nil
}
