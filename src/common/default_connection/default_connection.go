package default_connection

import (
	"sync"

	"queue-manager/common/config"
	"queue-manager/common/rabbitmq"
)

// Global shared connection manager instance
var (
	MainExchangeName = "main_exchange"

	defaultConnection  *rabbitmq.Connection
	defaultManagerOnce sync.Once
)

// GetConnection is a convenience function to get a connection from the default connection manager
// This maintains backward compatibility with the old API
func GetConnection(connectionName string) (*rabbitmq.Connection, error) {
	var err error
	defaultManagerOnce.Do(func() {
		// Create connection details from config
		details := rabbitmq.ConnectionConfig{
			Name:     connectionName,
			Username: config.GetRabbitMQUsername(),
			Password: config.GetRabbitMQPassword(),
			Host:     config.GetRabbitMQHost(),
			Port:     config.GetRabbitMQPort(),
			VHost:    config.GetRabbitMQVHost(),
		}
		defaultConnection, err = rabbitmq.NewConnection(details)
	})
	return defaultConnection, err
}

// CloseAll closes the connection in the default connection manager
// This maintains backward compatibility with the old API
func CloseAll() {
	if defaultConnection != nil {
		defaultConnection.Close()
	}
}
