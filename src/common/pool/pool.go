package pool

import (
	"log/slog"
	"sync"
	"sync/atomic"
	"time"

	"github.com/cockroachdb/errors"
)

// ResourceFactory is a function that creates a new resource
type ResourceFactory[T any] func() (T, error)

// ResourceInfo tracks metadata for each resource
type ResourceInfo[T any] struct {
	Resource T
	LastUsed time.Time
	Index    int
	InUse    atomic.Bool
}

// PoolOptions configures the resource pool
type PoolOptions struct {
	// MaxSize is the maximum number of resources to allow in the pool
	MaxSize int
	// CleanupInterval is how often to check resource health and idle timeout
	CleanupInterval time.Duration
	// IdleTimeout is how long a resource can remain unused before being closed
	IdleTimeout time.Duration
	// InitialSize is the number of resources to create at startup (optional)
	InitialSize int
	// WaitTimeout is the maximum time to wait for an available resource
	// A zero value means wait indefinitely
	WaitTimeout time.Duration
}

// DefaultPoolOptions provides sensible defaults
func DefaultPoolOptions() PoolOptions {
	return PoolOptions{
		MaxSize:         10,
		CleanupInterval: 1 * time.Minute,
		IdleTimeout:     5 * time.Minute,
		InitialSize:     2,
		WaitTimeout:     30 * time.Second,
	}
}

// Pool manages a dynamic pool of resources with maximum size
type Pool[T any] struct {
	resources     []*ResourceInfo[T]
	mutex         sync.Mutex
	logger        *slog.Logger
	cleanupTicker *time.Ticker
	maxSize       int
	idleTimeout   time.Duration
	waitTimeout   time.Duration
	closed        atomic.Bool

	// Wait mechanism
	waiters     []chan T
	waiterMutex sync.Mutex

	// Resource management
	factory     ResourceFactory[T]
	cleanup     func(T) error
	healthCheck func(T) bool
}

// ResourcePool is the interface for a generic resource pool
type ResourcePool[T any] interface {
	// Get gets an available resource from the pool
	Get() (T, error)
	// GetWithTimeout gets a resource with a specific timeout
	GetWithTimeout(timeout time.Duration) (T, error)
	// Release returns a resource to the pool
	Release(resource T)
	// Close closes all resources in the pool
	Close()
	// GetStats returns current pool statistics
	GetStats() (current, inUse, waiters int)
}

// New creates a new dynamic resource pool
func New[T any](
	factory ResourceFactory[T],
	cleanup func(T) error,
	healthCheck func(T) bool,
	options PoolOptions,
) (*Pool[T], error) {
	logger := slog.With("component", "dynamic-resource-pool")

	pool := &Pool[T]{
		resources:   make([]*ResourceInfo[T], 0, options.MaxSize),
		mutex:       sync.Mutex{},
		logger:      logger,
		maxSize:     options.MaxSize,
		idleTimeout: options.IdleTimeout,
		waitTimeout: options.WaitTimeout,
		waiters:     make([]chan T, 0),
		waiterMutex: sync.Mutex{},
		factory:     factory,
		cleanup:     cleanup,
		healthCheck: healthCheck,
	}

	// Initialize initial resources if specified
	initialSize := options.InitialSize
	if initialSize > options.MaxSize {
		initialSize = options.MaxSize
	}

	for i := 0; i < initialSize; i++ {
		resource, err := factory()
		if err != nil {
			pool.Close()
			return nil, errors.Wrap(err, "failed to initialize initial resources in pool")
		}

		info := &ResourceInfo[T]{
			Resource: resource,
			LastUsed: time.Now(),
			Index:    i,
		}

		pool.resources = append(pool.resources, info)
	}

	// Start cleanup goroutine if interval is set
	if options.CleanupInterval > 0 {
		pool.cleanupTicker = time.NewTicker(options.CleanupInterval)
		go pool.startCleanupTimer()
	}

	logger.Info("Dynamic resource pool initialized",
		"maxSize", options.MaxSize,
		"initialSize", initialSize,
		"idleTimeout", options.IdleTimeout)
	return pool, nil
}

// startCleanupTimer periodically checks for failed resources and removes idle ones
func (p *Pool[T]) startCleanupTimer() {
	for range p.cleanupTicker.C {
		p.checkAndCleanupResources()
	}
}

// checkAndCleanupResources checks all resources in the pool, recreates failed ones, and removes idle ones
func (p *Pool[T]) checkAndCleanupResources() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	now := time.Now()
	var activeResources []*ResourceInfo[T]

	for _, info := range p.resources {
		// Skip resources that are currently in use
		if info.InUse.Load() {
			activeResources = append(activeResources, info)
			continue
		}

		// Check if resource has exceeded idle timeout
		if now.Sub(info.LastUsed) > p.idleTimeout {
			p.logger.Debug("Closing idle resource", "index", info.Index, "idleTime", now.Sub(info.LastUsed))
			if err := p.cleanup(info.Resource); err != nil {
				p.logger.Error("Error closing idle resource", "error", err, "index", info.Index)
			}
			continue
		}

		// Check if resource is unhealthy and recreate if needed
		if !p.healthCheck(info.Resource) {
			p.logger.Warn("Detected unhealthy resource, recreating", "index", info.Index)

			// Clean up the old resource first
			if err := p.cleanup(info.Resource); err != nil {
				p.logger.Error("Failed to clean up unhealthy resource", "error", err, "index", info.Index)
			}

			// Create a new resource
			newResource, err := p.factory()
			if err != nil {
				p.logger.Error("Failed to recreate resource", "error", err, "index", info.Index)
				continue
			}

			// Update the resource info
			info.Resource = newResource
			info.LastUsed = now
		}

		activeResources = append(activeResources, info)
	}

	p.resources = activeResources
	p.logger.Debug("Resource pool cleanup completed",
		"activeResources", len(activeResources),
		"maxSize", p.maxSize)
}

// Get gets an available resource from the pool, creates a new one if needed,
// or waits until one becomes available if the pool is at capacity
func (p *Pool[T]) Get() (T, error) {
	return p.GetWithTimeout(p.waitTimeout)
}

// GetWithTimeout gets a resource with a specific timeout that overrides the pool's default
// A zero timeout means wait indefinitely
func (p *Pool[T]) GetWithTimeout(timeout time.Duration) (T, error) {
	// Check if pool is closed
	if p.closed.Load() {
		var zero T
		return zero, errors.New("pool is closed")
	}

	// First attempt to get a resource without waiting
	resource, waitCh, err := p.tryGet()
	if err != nil {
		var zero T
		return zero, err // Return critical errors immediately
	}

	// If we got a resource directly, return it
	if waitCh == nil {
		return resource, nil
	}

	// If timeout is zero, wait indefinitely
	if timeout <= 0 {
		res, ok := <-waitCh
		if !ok {
			var zero T
			return zero, errors.New("wait channel closed, pool is shutting down")
		}
		return res, nil
	}

	// Otherwise wait with timeout
	select {
	case res, ok := <-waitCh:
		if !ok {
			var zero T
			return zero, errors.New("wait channel closed, pool is shutting down")
		}
		return res, nil
	case <-time.After(timeout):
		// Remove ourselves from the wait list
		p.removeWaiter(waitCh)
		var zero T
		return zero, errors.New("timed out waiting for available resource")
	}
}

// tryGet attempts to get a resource immediately or returns a wait channel
// Returns: (resource, nil, nil) if a resource was obtained
//
//	(zero, waitCh, nil) if no resource is available and needs to wait
//	(zero, nil, err) if there was an error
func (p *Pool[T]) tryGet() (T, chan T, error) {
	p.mutex.Lock()
	var zero T

	// First try to find an existing available resource
	for _, info := range p.resources {
		// Try to mark as in-use atomically
		if info.InUse.CompareAndSwap(false, true) {
			info.LastUsed = time.Now()
			p.mutex.Unlock()
			return info.Resource, nil, nil
		}
	}

	// If all resources are in use, check if we can create a new one
	if len(p.resources) < p.maxSize {
		// Create a new resource
		p.logger.Info("Creating new resource on demand", "currentSize", len(p.resources), "maxSize", p.maxSize)
		resource, err := p.factory()
		if err != nil {
			p.mutex.Unlock()
			return zero, nil, errors.Wrap(err, "failed to create new resource on demand")
		}

		// Add it to the pool and mark as in use
		info := &ResourceInfo[T]{
			Resource: resource,
			LastUsed: time.Now(),
			Index:    len(p.resources),
		}
		info.InUse.Store(true)
		p.resources = append(p.resources, info)

		p.mutex.Unlock()
		return resource, nil, nil
	}

	p.mutex.Unlock()

	// Pool is at capacity, create a waiter and add to wait list
	waitCh := make(chan T, 1)
	p.addWaiter(waitCh)

	return zero, waitCh, nil
}

// addWaiter adds a waiter to the wait list
func (p *Pool[T]) addWaiter(waitCh chan T) {
	p.waiterMutex.Lock()
	defer p.waiterMutex.Unlock()

	p.waiters = append(p.waiters, waitCh)
	p.logger.Debug("Added resource waiter", "waiters", len(p.waiters))
}

// removeWaiter removes a waiter from the wait list
func (p *Pool[T]) removeWaiter(waitCh chan T) {
	p.waiterMutex.Lock()
	defer p.waiterMutex.Unlock()

	for i, ch := range p.waiters {
		if ch == waitCh {
			// Remove this waiter
			p.waiters = append(p.waiters[:i], p.waiters[i+1:]...)
			p.logger.Debug("Removed resource waiter", "waiters", len(p.waiters))
			return
		}
	}
}

// Release marks the resource as available and notifies any waiters
func (p *Pool[T]) Release(resource T) {
	p.mutex.Lock()

	var resourceReleased bool
	var resourceInfo *ResourceInfo[T]

	// Find the resource and mark it as not in use
	for _, info := range p.resources {
		// First try to use type-specific equality if available
		// Check if the type implements an Equal method
		type equalizer interface {
			Equal(other any) bool
		}

		// Try using Equal method if available
		if eq, ok := any(info.Resource).(equalizer); ok {
			if eq.Equal(any(resource)) {
				info.InUse.Store(false)
				info.LastUsed = time.Now()
				resourceReleased = true
				resourceInfo = info
				break
			}
		} else if any(info.Resource) == any(resource) {
			// Fallback to direct comparison (works for primitive types and pointers)
			info.InUse.Store(false)
			info.LastUsed = time.Now()
			resourceReleased = true
			resourceInfo = info
			break
		}
	}

	p.mutex.Unlock()

	if !resourceReleased {
		p.logger.Warn("Attempted to release a resource that doesn't belong to this pool")
		return
	}

	// Check if there are any waiters for a resource
	p.waiterMutex.Lock()
	if len(p.waiters) > 0 {
		// Get oldest waiter
		waiter := p.waiters[0]
		p.waiters = p.waiters[1:]
		p.waiterMutex.Unlock()

		// Mark the resource as in use again for this waiter
		resourceInfo.InUse.Store(true)

		// Send the resource to the waiter
		select {
		case waiter <- resource:
			p.logger.Debug("Provided released resource to waiter")
		default:
			// Waiter no longer waiting, release the resource back
			p.logger.Debug("Waiter no longer waiting, releasing resource back to pool")
			resourceInfo.InUse.Store(false)
		}
	} else {
		p.waiterMutex.Unlock()
	}
}

// GetStats returns current pool statistics
func (p *Pool[T]) GetStats() (current, inUse, waiters int) {
	p.mutex.Lock()
	current = len(p.resources)
	for _, info := range p.resources {
		if info.InUse.Load() {
			inUse++
		}
	}
	p.mutex.Unlock()

	p.waiterMutex.Lock()
	waiters = len(p.waiters)
	p.waiterMutex.Unlock()

	return current, inUse, waiters
}

// Close closes all resources in the pool and notifies all waiters
func (p *Pool[T]) Close() {
	p.closed.Store(true)

	if p.cleanupTicker != nil {
		p.cleanupTicker.Stop()
	}

	// Notify all waiters with an error by closing their channels
	p.waiterMutex.Lock()
	for _, waiter := range p.waiters {
		close(waiter)
	}
	p.waiters = nil
	p.waiterMutex.Unlock()

	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Close all resources
	for _, info := range p.resources {
		if err := p.cleanup(info.Resource); err != nil {
			p.logger.Error("Error closing resource during pool shutdown", "error", err)
		}
	}

	p.resources = nil
	p.logger.Info("Resource pool closed")
}
