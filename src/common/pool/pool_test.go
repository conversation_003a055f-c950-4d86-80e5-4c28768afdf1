package pool

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockResource represents a test resource with metadata
type MockResource struct {
	ID        int
	Healthy   bool
	Closed    bool
	CreatedAt time.Time
}

// TestBasicPoolOperations tests basic pool operations (get, release)
func TestBasicPoolOperations(t *testing.T) {
	// Set up resource factory and cleanup functions
	var resourceCounter int
	factory := func() (*MockResource, error) {
		resourceCounter++
		return &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}, nil
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool
	options := DefaultPoolOptions()
	options.InitialSize = 2
	options.MaxSize = 5
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)
	defer pool.Close()

	// Test getting resources
	r1, err := pool.Get()
	require.NoError(t, err)
	assert.Equal(t, 1, r1.ID)
	assert.False(t, r1.Closed)

	r2, err := pool.Get()
	require.NoError(t, err)
	assert.Equal(t, 2, r2.ID)
	assert.False(t, r2.Closed)

	// Test resource creation on demand
	r3, err := pool.Get()
	require.NoError(t, err)
	assert.Equal(t, 3, r3.ID)
	assert.False(t, r3.Closed)

	// Check pool stats
	current, inUse, waiters := pool.GetStats()
	assert.Equal(t, 3, current)
	assert.Equal(t, 3, inUse)
	assert.Equal(t, 0, waiters)

	// Release a resource
	pool.Release(r1)

	// Check stats again
	current, inUse, waiters = pool.GetStats()
	assert.Equal(t, 3, current)
	assert.Equal(t, 2, inUse)
	assert.Equal(t, 0, waiters)

	// Get the released resource
	r4, err := pool.Get()
	require.NoError(t, err)
	assert.Equal(t, 1, r4.ID) // Should get r1 back
	assert.False(t, r4.Closed)
}

// TestPoolMaxSize verifies that the pool respects the maximum size
func TestPoolMaxSize(t *testing.T) {
	// Set up resource factory and cleanup functions
	var resourceCounter int
	factory := func() (*MockResource, error) {
		resourceCounter++
		return &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}, nil
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool with small max size
	options := DefaultPoolOptions()
	options.InitialSize = 0
	options.MaxSize = 2
	options.WaitTimeout = 100 * time.Millisecond
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)
	defer pool.Close()

	// Get two resources (should succeed)
	r1, err := pool.Get()
	require.NoError(t, err)
	_, err = pool.Get()
	require.NoError(t, err)

	// Try to get a third resource (should wait and timeout)
	_, err = pool.Get()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "timed out waiting")

	// Release one resource
	pool.Release(r1)

	// Now should be able to get another resource
	r3, err := pool.Get()
	require.NoError(t, err)
	assert.Equal(t, r1.ID, r3.ID) // Should get the same resource back

	// Check stats
	current, inUse, waiters := pool.GetStats()
	assert.Equal(t, 2, current)
	assert.Equal(t, 2, inUse)
	assert.Equal(t, 0, waiters)
}

// TestPoolWaiting tests waiting for resources
func TestPoolWaiting(t *testing.T) {
	// Set up resource factory and cleanup functions
	var resourceCounter int
	factory := func() (*MockResource, error) {
		resourceCounter++
		return &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}, nil
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool with small max size
	options := DefaultPoolOptions()
	options.InitialSize = 0
	options.MaxSize = 1
	options.WaitTimeout = 2 * time.Second
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)
	defer pool.Close()

	// Get the only resource
	r1, err := pool.Get()
	require.NoError(t, err)

	// Set up a background goroutine to release after delay
	go func() {
		time.Sleep(200 * time.Millisecond)
		pool.Release(r1)
	}()

	// Try to get a resource (should wait and then succeed)
	start := time.Now()
	r2, err := pool.Get()
	elapsed := time.Since(start)

	require.NoError(t, err)
	assert.Equal(t, r1.ID, r2.ID) // Should get the same resource back
	assert.GreaterOrEqual(t, elapsed.Milliseconds(), int64(200))
}

// TestResourceHealth tests the health check functionality
func TestResourceHealth(t *testing.T) {
	// Create a map of resources for tracking
	resources := make(map[int]*MockResource)
	var resourceCounter int
	var mu sync.Mutex

	factory := func() (*MockResource, error) {
		mu.Lock()
		defer mu.Unlock()
		resourceCounter++
		r := &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}
		resources[r.ID] = r
		return r, nil
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool with cleanup interval
	options := DefaultPoolOptions()
	options.InitialSize = 1
	options.MaxSize = 1
	options.CleanupInterval = 100 * time.Millisecond
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)
	defer pool.Close()

	// Get the resource and corrupt it
	r1, err := pool.Get()
	require.NoError(t, err)
	r1.Healthy = false
	pool.Release(r1)

	// Wait for cleanup cycle
	time.Sleep(200 * time.Millisecond)

	// Get a resource - should be a new one
	r2, err := pool.Get()
	require.NoError(t, err)
	assert.NotEqual(t, r1.ID, r2.ID)
	assert.True(t, r2.Healthy)
	assert.True(t, resources[r1.ID].Closed) // Original resource should be closed
}

// TestIdleTimeout tests that idle resources are removed
func TestIdleTimeout(t *testing.T) {
	// Create resources
	var resourceCounter int
	factory := func() (*MockResource, error) {
		resourceCounter++
		return &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}, nil
	}

	var closedResources []int
	cleanup := func(r *MockResource) error {
		r.Closed = true
		closedResources = append(closedResources, r.ID)
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool with short idle timeout
	options := DefaultPoolOptions()
	options.InitialSize = 2
	options.MaxSize = 5
	options.IdleTimeout = 200 * time.Millisecond
	options.CleanupInterval = 100 * time.Millisecond
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)
	defer pool.Close()

	// Get the resources
	r1, err := pool.Get()
	require.NoError(t, err)
	r2, err := pool.Get()
	require.NoError(t, err)

	// Release both
	pool.Release(r1)
	pool.Release(r2)

	// Wait for idle timeout
	time.Sleep(400 * time.Millisecond)

	// Resources should be closed
	current, inUse, waiters := pool.GetStats()
	assert.Equal(t, 0, current)
	assert.Equal(t, 0, inUse)
	assert.Equal(t, 0, waiters)

	assert.Len(t, closedResources, 2)
	assert.Contains(t, closedResources, 1)
	assert.Contains(t, closedResources, 2)
}

// TestConcurrency tests concurrent access to the pool
func TestConcurrency(t *testing.T) {
	// Create resources
	var resourceCounter int
	var mu sync.Mutex

	factory := func() (*MockResource, error) {
		mu.Lock()
		defer mu.Unlock()
		resourceCounter++
		return &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}, nil
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool with limited size
	options := DefaultPoolOptions()
	options.InitialSize = 0
	options.MaxSize = 5
	options.WaitTimeout = 2 * time.Second
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)
	defer pool.Close()

	// Run concurrent operations
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			// Get resource
			resource, err := pool.Get()
			if err != nil {
				t.Errorf("Error getting resource in goroutine %d: %v", id, err)
				return
			}

			// Use resource briefly
			time.Sleep(50 * time.Millisecond)

			// Release resource
			pool.Release(resource)
		}(i)
	}

	wg.Wait()

	// After all goroutines finish, resources should be back in the pool
	current, inUse, waiters := pool.GetStats()
	assert.Equal(t, 5, current) // Should create up to max size
	assert.Equal(t, 0, inUse)   // All should be released
	assert.Equal(t, 0, waiters) // No waiters left
}

// TestFactoryFailure tests how the pool handles resource creation failures
func TestFactoryFailure(t *testing.T) {
	// Create factory that fails after the first resource
	var resourceCounter int
	factory := func() (*MockResource, error) {
		resourceCounter++
		if resourceCounter > 1 {
			return nil, fmt.Errorf("simulated factory error")
		}
		return &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}, nil
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool
	options := DefaultPoolOptions()
	options.InitialSize = 1 // This will succeed
	options.MaxSize = 5
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)
	defer pool.Close()

	// First Get should work
	r1, err := pool.Get()
	require.NoError(t, err)
	assert.Equal(t, 1, r1.ID)

	// Second Get should fail
	_, err = pool.Get()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "simulated factory error")
}

// TestGetWithCustomTimeout tests the GetWithTimeout method
func TestGetWithCustomTimeout(t *testing.T) {
	// Create resources
	var resourceCounter int
	factory := func() (*MockResource, error) {
		resourceCounter++
		return &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}, nil
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool with long default timeout
	options := DefaultPoolOptions()
	options.InitialSize = 1
	options.MaxSize = 1
	options.WaitTimeout = 10 * time.Second // Very long default
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)
	defer pool.Close()

	// Get the only resource
	r1, err := pool.Get()
	require.NoError(t, err)

	// Try to get with shorter custom timeout
	start := time.Now()
	_, err = pool.GetWithTimeout(100 * time.Millisecond)
	elapsed := time.Since(start)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "timed out waiting")
	assert.Less(t, elapsed, 500*time.Millisecond) // Should use the custom timeout, not default

	// Release the resource
	pool.Release(r1)

	// Now Get should succeed quickly
	_, err = pool.GetWithTimeout(100 * time.Millisecond)
	require.NoError(t, err)
}

// TestPoolClose tests the Close method and resources cleanup
func TestPoolClose(t *testing.T) {
	// Create resources
	var resourceCounter int
	var closedCount int

	factory := func() (*MockResource, error) {
		resourceCounter++
		return &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}, nil
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		closedCount++
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool and get some resources
	options := DefaultPoolOptions()
	options.InitialSize = 3
	options.MaxSize = 5
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)

	// Get a resource
	r1, err := pool.Get()
	require.NoError(t, err)

	// Close the pool
	pool.Close()

	// Check that all resources were closed
	assert.Equal(t, 3, closedCount)

	// Try using the pool after closing (should fail or do nothing)
	pool.Release(r1) // Should not panic

	_, err = pool.Get()
	assert.Error(t, err)
}

// TestReleaseNonPoolResource tests releasing a resource not from the pool
func TestReleaseNonPoolResource(t *testing.T) {
	// Create resources
	var resourceCounter int
	factory := func() (*MockResource, error) {
		resourceCounter++
		return &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}, nil
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool
	options := DefaultPoolOptions()
	options.InitialSize = 1
	options.MaxSize = 2
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)
	defer pool.Close()

	// Create a resource not from the pool
	foreignResource := &MockResource{ID: 999}

	// Release the foreign resource (should log a warning but not panic)
	pool.Release(foreignResource)

	// Pool stats should remain unchanged
	current, inUse, waiters := pool.GetStats()
	assert.Equal(t, 1, current)
	assert.Equal(t, 0, inUse)
	assert.Equal(t, 0, waiters)
}

// TestInitialSizeGreaterThanMaxSize tests initialization when initialSize > maxSize
func TestInitialSizeGreaterThanMaxSize(t *testing.T) {
	// Create resources
	var resourceCounter int
	factory := func() (*MockResource, error) {
		resourceCounter++
		return &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}, nil
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool with initialSize > maxSize
	options := DefaultPoolOptions()
	options.InitialSize = 10
	options.MaxSize = 5
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)
	defer pool.Close()

	// Check that only maxSize resources were created
	current, inUse, waiters := pool.GetStats()
	assert.Equal(t, 5, current)
	assert.Equal(t, 0, inUse)
	assert.Equal(t, 0, waiters)
}

// TestInitializationFailure tests pool creation when the factory fails
func TestInitializationFailure(t *testing.T) {
	// Create factory that always fails
	factory := func() (*MockResource, error) {
		return nil, fmt.Errorf("simulated factory error")
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool
	options := DefaultPoolOptions()
	options.InitialSize = 2
	options.MaxSize = 5
	_, err := New(factory, cleanup, healthCheck, options)

	// Should fail with error
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "simulated factory error")
}

// TestWaiterRemoval tests if waiters are properly removed on timeout
func TestWaiterRemoval(t *testing.T) {
	// Create resources
	var resourceCounter int
	factory := func() (*MockResource, error) {
		resourceCounter++
		return &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}, nil
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	// Create pool with very small size
	options := DefaultPoolOptions()
	options.InitialSize = 1
	options.MaxSize = 1
	options.WaitTimeout = 100 * time.Millisecond
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)
	defer pool.Close()

	// Get the only resource
	r1, err := pool.Get()
	require.NoError(t, err)

	// Start three goroutines that try to get a resource (all should fail with timeout)
	var wg sync.WaitGroup
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			_, err := pool.Get()
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "timed out waiting")
		}()
	}

	// Wait a bit for them to register as waiters
	time.Sleep(50 * time.Millisecond)

	// Check waiters count
	_, _, waiters := pool.GetStats()
	assert.Equal(t, 3, waiters)

	// Wait for all timeouts
	wg.Wait()

	// All waiters should be gone
	_, _, waiters = pool.GetStats()
	assert.Equal(t, 0, waiters)

	// Release the resource
	pool.Release(r1)

	// Stats should show no waiters and no in-use resources
	current, inUse, waiters := pool.GetStats()
	assert.Equal(t, 1, current)
	assert.Equal(t, 0, inUse)
	assert.Equal(t, 0, waiters)
}

// TestResourcePoolInterface ensures that Pool properly implements ResourcePool
func TestResourcePoolInterface(t *testing.T) {
	// Create resources
	var resourceCounter int
	factory := func() (*MockResource, error) {
		resourceCounter++
		return &MockResource{
			ID:        resourceCounter,
			Healthy:   true,
			CreatedAt: time.Now(),
		}, nil
	}

	cleanup := func(r *MockResource) error {
		r.Closed = true
		return nil
	}

	healthCheck := func(r *MockResource) bool {
		return r.Healthy
	}

	options := DefaultPoolOptions()
	options.InitialSize = 1
	pool, err := New(factory, cleanup, healthCheck, options)
	require.NoError(t, err)

	// Verify the concrete type implements the interface
	var _ ResourcePool[*MockResource] = pool
}
