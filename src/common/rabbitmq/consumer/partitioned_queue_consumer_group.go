package consumer

import (
	"fmt"
	"log/slog"
	"math/rand"
	"strings"
	"sync"
	"time"

	"github.com/cockroachdb/errors"

	"queue-manager/common/config"
	"queue-manager/common/queue"
	"queue-manager/common/rabbitmq"
	"queue-manager/common/rabbitmq/topology"
)

// PartitionedQueueConsumerGroup consumes messages from partitioned queues
type PartitionedQueueConsumerGroup struct {
	*BaseQueueConsumerGroup
	exchangeName        string     // Main exchange name
	baseQueueName       string     // Base name for partitioned queues
	numPartitions       int        // Number of partitions for the queue
	topics              []string   // Topics to consume from
	workersPerPartition int        // Number of workers per partition (usually 1)
	mutex               sync.Mutex // Additional mutex for partitioned-specific operations
}

// NewPartitionedQueueConsumerGroup creates a consumer for a partitioned queue
func NewPartitionedQueueConsumerGroup(
	connection *rabbitmq.Connection,
	exchangeName string,
	baseQueueName string,
	topics []string,
	numPartitions int,
	handler queue.MessageH<PERSON><PERSON>,
	maxRetries int,
	processingTimeout time.Duration,
) (queue.ConsumerGroup, error) {
	if numPartitions < 1 {
		return nil, errors.New("number of partitions must be at least 1")
	}

	logger := slog.With(
		"component", "partitioned-queue-consumer",
		"baseQueue", baseQueueName,
		"partitions", numPartitions,
	)

	top := topology.NewPartitionedQueueDefinition(exchangeName, baseQueueName, numPartitions, topics)

	return &PartitionedQueueConsumerGroup{
		BaseQueueConsumerGroup: NewBaseQueueConsumerGroup(connection, handler, maxRetries, top, processingTimeout, logger),
		exchangeName:           exchangeName,
		baseQueueName:          baseQueueName,
		topics:                 topics,
		numPartitions:          numPartitions,
		workersPerPartition:    1, // count be more because we use x-single-active-consumer, but let's stick with 1
	}, nil
}

func (c *PartitionedQueueConsumerGroup) Start() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.isRunning {
		c.logger.Info("Consumer is already running")
		return nil
	}

	// Setup topology
	if err := c.setupTopology(); err != nil {
		return errors.Wrap(err, "failed to set up retry for partitioned queue consumer")
	}

	// Setup publishing channel pool for retries and DLQ
	if err := c.setupPubChannelPool(); err != nil {
		return errors.Wrap(err, "failed to set up pub channel pool for partitioned queue consumer")
	}

	c.logger.Info("Starting consumption from all partitions")

	// Start consumers for each partition
	for partitionIndex, partitionName := range c.topology.GetPartitionQueueNames() {
		// Optional random sleep so concurrent consumer instances have a chance to get some queues
		// This can be enabled via CONSUMER_STARTUP_SLEEP_ENABLED environment variable
		if config.GetConsumerStartupSleepEnabled() {
			time.Sleep(time.Duration(rand.Intn(100)+1) * time.Millisecond)
		}

		c.logger.Debug("Starting consumers for partition",
			"partition", partitionIndex+1,
			"queueName", partitionName)

		// Create workers for this partition
		for workerIndex := 0; workerIndex < c.workersPerPartition; workerIndex++ {
			consumerName := fmt.Sprintf("%s-partition-%d-worker-%d",
				c.baseQueueName, partitionIndex+1, workerIndex+1)

			c.logger.Debug("Creating consumer", "name", consumerName)

			// Create consumer for this partition with simplified parameters
			consumer, err := NewConsumer(
				c.connection,
				c.pubChannelPool,
				consumerName,
				partitionName, // Use partition queue name directly
				c.topology,    // Pass the topology
				c.handler,
				1,
				c.retryPolicy,
				c.processingTimeout,
			)
			if err != nil {
				c.logger.Error("Failed to create consumer",
					"partition", partitionIndex+1,
					"worker", workerIndex+1,
					"error", err)

				// Stop any consumers that were successfully started
				c.StopAllConsumers()
				return errors.Wrapf(err, "failed to create consumer %s", consumerName)
			}

			// Start the consumer
			if err := consumer.Start(); err != nil {
				if strings.Contains(err.Error(), "exclusive use") {
					c.logger.Info("Failed to start consumer, queue has an exclusive consumer already.",
						"name", consumerName,
						"error", err)

					if err := consumer.Stop(); err != nil {
						c.logger.Error("Error stopping consumer", "error", err)
					}
					continue
				}

				c.logger.Error("Failed to start consumer",
					"name", consumerName,
					"error", err)

				if err := consumer.Stop(); err != nil { // Try to clean up this one
					c.logger.Error("Error stopping consumer", "error", err)
				}
				c.StopAllConsumers()
				return errors.Wrapf(err, "failed to start consumer %s", consumerName)
			}

			c.consumers = append(c.consumers, consumer)
			c.logger.Debug("Consumer started successfully", "name", consumerName)
		}
	}

	// Start the retry queue cleanup routine
	//c.startRetryQueueCleanup()

	c.isRunning = true
	c.logger.Info("Started consuming from all partitions",
		"partitionCount", c.numPartitions,
		"totalConsumers", len(c.consumers))

	return nil
}
