package consumer

import (
	"log/slog"
	"sync"
	"time"

	"github.com/cockroachdb/errors"

	"queue-manager/common/queue"
	"queue-manager/common/rabbitmq"
	"queue-manager/common/rabbitmq/channel_pool"
	"queue-manager/common/rabbitmq/topology"
)

// BaseQueueConsumerGroup provides common functionality for all consumer types
type BaseQueueConsumerGroup struct {
	consumers         []*Consumer              // list of consumers for the queue
	mutex             sync.Mutex               // mutex to Start and Stop
	isRunning         bool                     // flag to indicate if the consumer is running
	maxRetries        int                      // maximum number of retries
	handler           queue.MessageHandler     // handler for processing messages
	retryPolicy       queue.RetryPolicy        // retry policy
	topology          topology.Topology        // topology for the queue
	logger            *slog.Logger             // logger for the consumer
	connection        *rabbitmq.Connection     // connection to the broker
	pubChannelPool    channel_pool.ChannelPool // channel pool for publishing retries and DLQ
	processingTimeout time.Duration            // timeout for processing messages
	exchangeSetup     topology.ExchangeSetup
}

// NewBaseQueueConsumerGroup creates a new base consumer
func NewBaseQueueConsumerGroup(
	connection *rabbitmq.Connection,
	handler queue.MessageHandler,
	maxRetries int,
	top topology.Topology,
	processingTimeout time.Duration,
	logger *slog.Logger,
) *BaseQueueConsumerGroup {
	return NewBaseQueueConsumerGroupWithExchangeSetup(
		connection,
		handler,
		maxRetries,
		top,
		processingTimeout,
		logger,
		topology.NewDefaultExchangeSetup(top.GetExchangeName()),
	)
}

// NewBaseQueueConsumerGroupWithExchangeSetup creates a new base consumer with custom exchange setup
func NewBaseQueueConsumerGroupWithExchangeSetup(
	connection *rabbitmq.Connection,
	handler queue.MessageHandler,
	maxRetries int,
	topology topology.Topology,
	processingTimeout time.Duration,
	logger *slog.Logger,
	exchangeSetup topology.ExchangeSetup,
) *BaseQueueConsumerGroup {
	return &BaseQueueConsumerGroup{
		consumers:         make([]*Consumer, 0),
		handler:           handler,
		topology:          topology,
		logger:            logger,
		maxRetries:        maxRetries,
		retryPolicy:       NewExponentialBackoff(maxRetries),
		connection:        connection,
		processingTimeout: processingTimeout,
		exchangeSetup:     exchangeSetup,
	}
}

func (c *BaseQueueConsumerGroup) setupPubChannelPool() error {
	// Create a thread-safe channel pool with confirms if needed
	poolOptions := channel_pool.DefaultChannelPoolOptions()
	poolOptions.MaxSize = 100
	poolOptions.EnableConfirms = true
	pubChannelPool, err := channel_pool.NewChannelPool(c.connection, poolOptions)
	if err != nil {
		return errors.Wrap(err, "failed to create channel pool")
	}
	c.pubChannelPool = pubChannelPool

	return nil
}

// SetupTopology configures the queue topology with DLQ capabilities
func (c *BaseQueueConsumerGroup) setupTopology() error {
	err := c.exchangeSetup.SetupMainExchange(c.connection)
	if err != nil {
		return errors.Wrap(err, "failed to setup main exchange")
	}

	// Pass connection to topology setup - each topology will create its own channel
	if err := c.topology.Setup(c.connection); err != nil {
		return errors.Wrap(err, "failed to setup queue topology")
	}

	if err := c.topology.SetupRetryQueues(c.connection, c.retryPolicy); err != nil {
		return errors.Wrap(err, "failed to setup retry queues topology")
	}

	c.logger.Info("Set up queue topology with on-demand retry and DLQ",
		"exchangeName", c.topology.GetExchangeName(),
		"dlqQueue", c.topology.GetDLQName())

	return nil
}

// Stop halts consumption from all partition queues
func (c *BaseQueueConsumerGroup) Stop() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.isRunning {
		c.logger.Debug("Consumer is not running")
		return nil
	}

	c.logger.Info("Stopping all partition consumers")
	c.StopAllConsumers()

	return nil
}

// StopAllConsumers stops all consumers
func (c *BaseQueueConsumerGroup) StopAllConsumers() {
	// Stop all consumers
	for i, consumer := range c.consumers {
		c.logger.Debug("Stopping consumer", "index", i)
		if err := consumer.Stop(); err != nil {
			c.logger.Error("Error stopping consumer", "index", i, "error", err)
		}
	}

	c.consumers = nil
	c.isRunning = false
	c.logger.Info("All consumers stopped")
}

// IsRunning returns whether consumers are active
func (c *BaseQueueConsumerGroup) IsRunning() bool {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.isRunning
}
