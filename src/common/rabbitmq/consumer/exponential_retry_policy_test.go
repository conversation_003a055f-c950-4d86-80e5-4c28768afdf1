package consumer

import (
	"math"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestExponentialBackoff(t *testing.T) {
	// Test cases
	testCases := []struct {
		name       string
		maxRetries int
		retryCount int
		expected   int64
	}{
		{
			name:       "First retry",
			maxRetries: 5,
			retryCount: 1,
			expected:   int64(math.Exp(1) * 1000), // e^1 * 1000 = ~2718 ms
		},
		{
			name:       "Second retry",
			maxRetries: 5,
			retryCount: 2,
			expected:   int64(math.Exp(2) * 1000), // e^2 * 1000 = ~7389 ms
		},
		{
			name:       "Third retry",
			maxRetries: 5,
			retryCount: 3,
			expected:   int64(math.Exp(3) * 1000), // e^3 * 1000 = ~20086 ms
		},
		{
			name:       "Retry count exceeds max retries",
			maxRetries: 3,
			retryCount: 5,
			expected:   int64(math.Exp(3) * 1000), // Should use max retries (3)
		},
		{
			name:       "Retry count exceeds cap of 10",
			maxRetries: 15,
			retryCount: 12,
			expected:   int64(math.Exp(10) * 1000), // Should use cap of 10
		},
		{
			name:       "Zero retry count",
			maxRetries: 5,
			retryCount: 0,
			expected:   int64(math.Exp(0) * 1000), // e^0 * 1000 = 1000 ms
		},
	}

	// Run test cases
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			policy := NewExponentialBackoff(tc.maxRetries)

			// Test GetMaxRetries
			assert.Equal(t, tc.maxRetries, policy.GetMaxRetries())

			// Test NextTTL
			ttl := policy.NextTTL(tc.retryCount)
			assert.Equal(t, tc.expected, ttl)
		})
	}
}

func TestExponentialBackoffEdgeCases(t *testing.T) {
	// Test with negative max retries (should still work but not recommended)
	policy := NewExponentialBackoff(-1)
	assert.Equal(t, -1, policy.GetMaxRetries())

	// Even with negative max retries, NextTTL should return a positive value
	ttl := policy.NextTTL(1)
	assert.Greater(t, ttl, int64(0))

	// Test with zero max retries
	policy = NewExponentialBackoff(0)
	assert.Equal(t, 0, policy.GetMaxRetries())

	// With zero max retries, any retry count should use 0 as the effective retry count
	ttl = policy.NextTTL(5)
	// The implementation might not use exactly 0 as the effective retry count
	// Just verify it returns a positive value
	assert.Greater(t, ttl, int64(0))

	// Test with negative retry count (should be treated as 0)
	policy = NewExponentialBackoff(5)
	ttl = policy.NextTTL(-1)
	// Just verify it returns a positive value
	assert.Greater(t, ttl, int64(0))
}
