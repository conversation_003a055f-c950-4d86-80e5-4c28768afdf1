package consumer

import (
	"math"
	
	"queue-manager/common/queue"
)

// ExponentialBackoff provides a simple exponential backoff mechanism
// implements queue.RetryPolicy
type ExponentialBackoff struct {
	MaxRetries int
}

// NewExponentialBackoff creates a new exponential backoff
func NewExponentialBackoff(maxRetries int) queue.RetryPolicy {
	return &ExponentialBackoff{
		MaxRetries: maxRetries,
	}
}

func (eb *ExponentialBackoff) GetMaxRetries() int {
	return eb.MaxRetries
}

// NextTTL calculates the TTL in milliseconds for the next retry
func (eb *ExponentialBackoff) NextTTL(retryCount int) int64 {
	// If retries exceed max, use max value
	if retryCount > eb.MaxRetries {
		retryCount = eb.MaxRetries
	}

	// Cap at 10 to prevent excessive exponential growth
	effectiveRetry := math.Min(10, float64(retryCount))

	// Base exponential value in milliseconds (no jitter)
	ttl := math.Exp(effectiveRetry) * 1000

	return int64(ttl)
}
