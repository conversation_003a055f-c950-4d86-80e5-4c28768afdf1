package consumer

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/cockroachdb/errors"
	amqp "github.com/rabbitmq/amqp091-go"

	"queue-manager/common/queue"
	"queue-manager/common/rabbitmq"
	"queue-manager/common/rabbitmq/channel_pool"
	"queue-manager/common/rabbitmq/topology"
)

// Consumer handles message consumption from RabbitMQ
type Consumer struct {
	name              string
	connection        *rabbitmq.Connection
	pubChannelPool    channel_pool.ChannelPool
	channel           *amqp.Channel
	handler           queue.MessageHandler
	queueName         string
	topology          topology.Topology // Reference to the topology
	retryPolicy       queue.RetryPolicy
	mutex             sync.Mutex
	consumeTag        string
	prefetchCount     int
	processingTimeout time.Duration
	done              chan struct{}
	logger            *slog.Logger
	notifyClosed      chan *amqp.Error // Channel for connection close notifications
	isReconnecting    bool             // Flag to prevent concurrent reconnection attempts
}

// NewConsumer creates a new RabbitMQ consumer
func NewConsumer(connection *rabbitmq.Connection, pubChannelPool channel_pool.ChannelPool, name string,
	queueName string, topology topology.Topology,
	handler queue.MessageHandler, prefetchCount int,
	retryPolicy queue.RetryPolicy, processingTimeout time.Duration) (*Consumer, error) {

	logger := slog.With(
		"component", "rabbitmq-consumer",
		"consumer", name,
		"queue", queueName,
	)

	consumer := &Consumer{
		name:              name,
		connection:        connection,
		pubChannelPool:    pubChannelPool,
		handler:           handler,
		queueName:         queueName,
		topology:          topology,
		retryPolicy:       retryPolicy,
		prefetchCount:     prefetchCount,
		processingTimeout: processingTimeout,
		done:              make(chan struct{}),
		logger:            logger,
	}

	// Create the initial channel
	if err := consumer.setupChannel(); err != nil {
		return nil, err
	}

	return consumer, nil
}

// setupChannel creates a new channel and sets up notifications
func (c *Consumer) setupChannel() error {
	// Create a channel
	channel, err := c.connection.Channel()
	if err != nil {
		return errors.Wrap(err, "failed to create channel")
	}

	// Check if the queue exists by inspecting it
	// We're using QueueInspect despite the deprecation warning
	// This is a safer approach than using QueueDeclare with passive flag
	// since we don't need to know all the original queue arguments
	// The alternative QueueDeclare with Passive:true requires matching all queue arguments
	// which is error-prone and can lead to PRECONDITION_FAILED errors
	// nolint:staticcheck // Using deprecated QueueInspect intentionally
	_, err = channel.QueueInspect(c.queueName)
	if err != nil {
		channel.Close()
		return errors.Wrapf(err, "queue '%s' does not exist", c.queueName)
	}

	// Set prefetch count (QoS)
	if err := channel.Qos(c.prefetchCount, 0, false); err != nil {
		channel.Close()
		return errors.Wrap(err, "failed to set QoS")
	}

	// Set up channel close notification
	c.notifyClosed = channel.NotifyClose(make(chan *amqp.Error, 1))
	c.channel = channel

	return nil
}

// Start begins consuming messages
func (c *Consumer) Start() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Ensure we have an open channel
	if c.channel == nil || c.channel.IsClosed() {
		c.logger.Info("Recreating channel for consuming")
		if err := c.setupChannel(); err != nil {
			return errors.Wrap(err, "failed to recreate channel for consuming")
		}
	}

	// Start consuming
	c.logger.Info("Starting to consume messages",
		"prefetchCount", c.prefetchCount,
		"processingTimeout", c.processingTimeout)

	deliveries, err := c.channel.Consume(
		c.queueName, // queue
		c.name,      // consumer tag
		false,       // auto-ack
		false,       // exclusive
		false,       // no-local
		false,       // no-wait
		nil,         // args
	)
	if err != nil {
		return errors.Wrap(err, "failed to start consuming")
	}

	c.consumeTag = c.name

	// Monitor channel status in a separate goroutine
	go c.monitorChannel()

	// Handle messages in a goroutine
	go c.handle(deliveries)

	c.logger.Info("Consumer started processing messages")
	return nil
}

// monitorChannel watches for channel closure and reconnects when needed
func (c *Consumer) monitorChannel() {
	for {
		select {
		case <-c.done:
			c.logger.Debug("Channel monitor received done signal, stopping")
			return
		case err := <-c.notifyClosed:
			if err == nil {
				c.logger.Info("Channel closed normally")
				return
			}

			c.logger.Warn("Channel closed unexpectedly", "error", err)

			// Prevent concurrent reconnection attempts
			c.mutex.Lock()
			if c.isReconnecting {
				c.mutex.Unlock()
				c.logger.Debug("Reconnection already in progress")
				return
			}
			c.isReconnecting = true
			c.mutex.Unlock()

			// Attempt to reconnect
			reconnecting := true
			for reconnecting {
				select {
				case <-c.done:
					c.logger.Debug("Reconnection aborted, consumer is stopping")
					return
				default:
					c.logger.Info("Attempting to restart consumer")

					if err := c.Start(); err != nil {
						c.logger.Error("Failed to restart consumer",
							"error", err,
							"retryInterval", rabbitmq.ReconnectInterval)
						time.Sleep(rabbitmq.ReconnectInterval)
						continue
					}

					c.mutex.Lock()
					c.isReconnecting = false
					c.mutex.Unlock()

					c.logger.Info("Successfully restarted consumer")
					reconnecting = false
				}
			}
		}
	}
}

// handle processes incoming messages sequentially (one at a time)
func (c *Consumer) handle(deliveries <-chan amqp.Delivery) {
	for {
		select {
		case <-c.done:
			c.logger.Debug("Consumer received done signal, stopping")
			return
		case msg, ok := <-deliveries:
			if !ok {
				c.logger.Warn("Delivery channel closed")
				// Just wait for the monitorChannel to handle reconnection
				return
			}

			// Log message received
			c.logger.Debug("Received message",
				"deliveryTag", msg.DeliveryTag,
				"exchange", msg.Exchange,
				"routingKey", msg.RoutingKey)

			// Process the message directly (no goroutine)
			if err := c.processMessage(msg); err != nil {
				c.logger.Error("Error processing message", "error", err)
			}
		}
	}
}

// processMessage handles a single delivery with retry logic
func (c *Consumer) processMessage(msg amqp.Delivery) error {
	msgLogger := c.logger.With(
		"maxRetries", c.retryPolicy.GetMaxRetries(),
		"deliveryTag", msg.DeliveryTag,
		"exchange", msg.Exchange,
		"routingKey", msg.RoutingKey,
	)

	// Create context with timeout for processing
	ctx, cancel := context.WithTimeout(context.Background(), c.processingTimeout)
	defer cancel()

	// Channel to receive handler results
	handlerChan := make(chan error, 1)
	var handlerErr error

	// Run handler in goroutine with panic recovery
	go func() {
		defer func() {
			if r := recover(); r != nil {
				msgLogger.Error("Panic occurred in message handler", "panic", r)
				// Convert any panic value to an error with stack trace
				if err, ok := r.(error); ok {
					handlerErr = errors.Wrapf(err, "panic in message handler")
				} else {
					handlerErr = errors.Newf("panic: %v", r)
				}

				// Get stack trace using cockroach errors
				msgLogger.Error("Stack trace from panic", "stack", errors.GetReportableStackTrace(handlerErr))

				// Send error to channel
				handlerChan <- handlerErr
			}
		}()

		// Process the message
		handlerChan <- c.handler(ctx, msg.Body)
	}()

	// Wait for either handler completion or context timeout
	select {
	case handlerErr = <-handlerChan:
		// Handler completed (either successfully or with error)
	case <-ctx.Done():
		// Context deadline exceeded or canceled
		msgLogger.Error("Message processing timed out", "timeout", c.processingTimeout)
		handlerErr = errors.Wrap(ctx.Err(), "message processing timed out")
	}

	// Handle any error (either regular, from panic, or from timeout)
	if handlerErr != nil {
		msgLogger.Error("Message processing failed, will handle failure",
			"error", handlerErr,
			"details", errors.FlattenDetails(handlerErr))
		return c.handleFailure(msg, handlerErr)
	}

	// Success - acknowledge the message
	msgLogger.Debug("Message processed successfully, acknowledging")
	return msg.Ack(false)
}

// handleFailure processes a failed message with retry logic
func (c *Consumer) handleFailure(msg amqp.Delivery, err error) error {
	msgLogger := c.logger.With(
		"maxRetries", c.retryPolicy.GetMaxRetries(),
		"deliveryTag", msg.DeliveryTag,
		"exchange", msg.Exchange,
		"routingKey", msg.RoutingKey,
	)

	// Extract retry count from headers or initialize if not present
	var retryCount = 0
	if count, ok := msg.Headers[rabbitmq.RetryCountHeader].(int32); ok {
		retryCount = int(count)
	}
	retryCount++

	msgLogger = msgLogger.With("attemptNumber", retryCount)

	// Check if we've reached max retries
	if retryCount > c.retryPolicy.GetMaxRetries() {
		msgLogger.Warn("Max retries reached, moving to dead letter queue")

		// Send to DLQ
		if dlqErr := c.sendToDLQ(msg.Body, retryCount, err.Error()); dlqErr != nil {
			msgLogger.Error("Failed to send to DLQ", "error", dlqErr)

			// If can't send to DLQ, reject the message without requeue
			if ackErr := msg.Reject(false); ackErr != nil {
				msgLogger.Error("Failed to reject message", "error", ackErr, "message", string(msg.Body))
			}
			return errors.Wrap(err, "failed to process message and send to DLQ")
		}

		msgLogger.Info("Successfully sent message to DLQ")
		// Acknowledge the original message since we've moved it to DLQ
		return msg.Ack(false)
	}

	// Calculate TTL for this retry
	ttl := c.retryPolicy.NextTTL(retryCount - 1)

	msgLogger = msgLogger.With("backoffMs", ttl)
	msgLogger.Info("Scheduling message retry", "retryCount", retryCount)

	// Update headers with retry information
	headers := amqp.Table{
		rabbitmq.RetryCountHeader: retryCount,
		rabbitmq.FirstTryHeader:   time.Now().Format(time.RFC3339),
		rabbitmq.LastTryHeader:    time.Now().Format(time.RFC3339),
		rabbitmq.LastRetryReason:  err.Error(),
	}
	// Preserve the first try timestamp if it exists
	if firstTry, ok := msg.Headers[rabbitmq.FirstTryHeader].(string); ok {
		headers[rabbitmq.FirstTryHeader] = firstTry
	}
	if hashKey, ok := msg.Headers[rabbitmq.ConsistentHashHeader].(string); ok {
		headers[rabbitmq.ConsistentHashHeader] = hashKey
	}
	// Store the original routing key so it can be used when the message is redelivered
	headers[rabbitmq.OriginalRoutingKey] = msg.RoutingKey

	// Send to retry queue
	if retryErr := c.sendToRetryQueue(msg.Body, headers, ttl); retryErr != nil {
		msgLogger.Error("Failed to send to retry queue", "error", retryErr)

		// If can't schedule retry, reject with requeue to try again
		if ackErr := msg.Reject(true); ackErr != nil {
			msgLogger.Error("Failed to reject message for retry", "error", ackErr)
		}
		return errors.Wrap(err, "failed to process message and schedule retry")
	}

	msgLogger.Info("Successfully scheduled message for retry")

	// Acknowledge the original message since we've scheduled a retry
	if ackErr := msg.Ack(false); ackErr != nil {
		msgLogger.Error("Failed to acknowledge message after scheduling retry", "error", ackErr)
		return errors.Wrap(ackErr, "failed to acknowledge message after scheduling retry")
	}

	return nil
}

// sendToRetryQueue publishes a message to the appropriate retry queue with TTL
// using the topology to ensure the queue exists
func (c *Consumer) sendToRetryQueue(body []byte, headers amqp.Table, ttlMs int64) error {
	logger := c.logger.With(
		"operation", "sendToRetryQueue",
		"ttlMs", ttlMs,
	)

	retryQueueName := c.topology.GetRetryQueueName(ttlMs)
	logger = logger.With("retryQueue", retryQueueName)

	// Get a channel for publishing
	ch, err := c.pubChannelPool.GetChannel()
	if err != nil {
		return errors.Wrap(err, "failed to get channel for retry queue setup")
	}
	defer c.pubChannelPool.Release(ch)

	// Create context for publishing
	ctx, cancel := context.WithTimeout(context.Background(), rabbitmq.DefaultPublishTimeout)
	defer cancel()

	// Convert TTL to string for the expiration property
	ttlStr := fmt.Sprintf("%d", ttlMs)

	// Create the publishing message with TTL
	msg := amqp.Publishing{
		Headers:      headers,
		ContentType:  "application/json",
		Body:         body,
		DeliveryMode: amqp.Persistent,
		Expiration:   ttlStr, // Set message TTL directly
	}

	// Publish directly to the retry queue
	err = ch.PublishWithContext(
		ctx,
		"",             // exchange (direct to queue)
		retryQueueName, // routing key (queue name)
		true,           // mandatory
		false,          // immediate
		msg,
	)

	if err != nil {
		logger.Error("Failed to publish to retry queue", "error", err)
		return errors.Wrap(err, "failed to publish to retry queue")
	}

	logger.Debug("Successfully published to retry queue with TTL", "ttlMs", ttlMs)
	return nil
}

// sendToDLQ publishes a failed message to the dead letter queue
func (c *Consumer) sendToDLQ(body []byte, retryCount int, reason string) error {
	dlqName := c.topology.GetDLQName()
	logger := c.logger.With(
		"operation", "sendToDLQ",
		"dlqQueue", dlqName,
		"retryCount", retryCount,
		"reason", reason,
	)

	// Get a channel for publishing
	ch, err := c.pubChannelPool.GetChannel()
	if err != nil {
		return errors.Wrap(err, "failed to get channel for DLQ")
	}
	defer c.pubChannelPool.Release(ch)

	ctx, cancel := context.WithTimeout(context.Background(), rabbitmq.DefaultPublishTimeout)
	defer cancel()

	// Record when it was moved to DLQ
	headers := amqp.Table{
		rabbitmq.RetryCountHeader: retryCount,
		rabbitmq.LastTryHeader:    time.Now().Format(time.RFC3339),
		rabbitmq.DLQReasonHeader:  reason,
	}

	logger.Debug("Publishing message to DLQ")

	// Publish to DLQ exchange
	err = ch.PublishWithContext(
		ctx,
		"",
		dlqName, // routing key
		true,
		false,
		amqp.Publishing{
			Headers:      headers,
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent,
		},
	)

	if err != nil {
		logger.Error("Failed to publish message to DLQ", "error", err)
		return errors.Wrap(err, "failed to publish to DLQ")
	}

	logger.Info("Successfully published message to DLQ")
	return nil
}

// Stop terminates message consumption
func (c *Consumer) Stop() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.logger.Info("Stopping consumer")

	// Signal all goroutines to stop
	close(c.done)

	if c.channel != nil && c.consumeTag != "" {
		// Cancel the consumer
		if err := c.channel.Cancel(c.consumeTag, false); err != nil {
			c.logger.Error("Failed to cancel consumer", "error", err)
			// Continue with cleanup
		}
		c.consumeTag = ""
	}

	if c.channel != nil {
		if err := c.channel.Close(); err != nil {
			c.logger.Error("Failed to close channel", "error", err)
			// Continue with cleanup
		}
		c.channel = nil
	}

	c.logger.Info("Consumer stopped successfully")
	return nil
}
