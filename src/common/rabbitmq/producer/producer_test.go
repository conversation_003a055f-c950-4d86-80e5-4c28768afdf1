package producer

import (
	"context"
	amqp "github.com/rabbitmq/amqp091-go"
	"log/slog"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"queue-manager/common/rabbitmq/channel_pool"
)

// Let's keep the mock implementations for Channel and Connection
type MockChannel struct {
	mock.Mock
}

// Implement all the necessary methods for the Channel interface
func (m *MockChannel) PublishWithContext(ctx context.Context, exchange, key string, mandatory, immediate bool, msg amqp.Publishing) error {
	args := m.Called(ctx, exchange, key, mandatory, immediate, msg)
	return args.Error(0)
}

func (m *MockChannel) Channel() *amqp.Channel {
	return nil // Not needed for this test
}

func (m *MockChannel) Close() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockChannel) IsClosed() bool {
	args := m.Called()
	return args.Bool(0)
}

// Mock channel pool that we can use with the real Producer
type MockChannelPool struct {
	channel_pool.ChannelPool
	mock.Mock
}

func (m *MockChannelPool) GetChannel() (channel_pool.Channel, error) {
	args := m.Called()
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	// We've already checked that args.Get(0) is not nil, so type assertion should be safe
	// But we'll add an explicit check to satisfy the linter
	val := args.Get(0)
	ch, ok := val.(channel_pool.Channel)
	if !ok {
		panic("unexpected type from args.Get(0)")
	}
	return ch, args.Error(1)
}

func (m *MockChannelPool) Release(channel channel_pool.Channel) {
	m.Called(channel)
}

func (m *MockChannelPool) Close() {
	m.Called()
}

// TestProducerPublish tests the real Producer with mocked dependencies
func TestProducerPublish(t *testing.T) {
	// Create mocks
	mockChannel := &MockChannel{}
	mockPool := &MockChannelPool{}

	// Setup expectations
	mockPool.On("GetChannel").Return(mockChannel, nil)
	mockPool.On("Release", mockChannel).Return()
	mockChannel.On("PublishWithContext", mock.Anything, "test-exchange", "test.topic", true, false, mock.Anything).Return(nil)

	// Create a real producer but inject our mock channel pool
	producer := &Producer{
		channelPool:    mockPool,
		exchangeName:   "test-exchange",
		publishTimeout: 1 * time.Second,
		useConfirms:    false,
		confirmTimeout: 1 * time.Second,
		logger:         slog.Default().With("component", "test-producer"),
	}

	// Test publishing a message
	err := producer.Publish("test.topic", []byte(`{"test":"data"}`))

	// Verify expectations
	assert.NoError(t, err)
	mockPool.AssertExpectations(t)
	mockChannel.AssertExpectations(t)
}
