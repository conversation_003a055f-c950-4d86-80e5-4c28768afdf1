package producer

import (
	"context"
	"log/slog"
	"queue-manager/common/rabbitmq/topology"
	"time"

	"github.com/cockroachdb/errors"
	amqp "github.com/rabbitmq/amqp091-go"

	"queue-manager/common/queue"
	"queue-manager/common/rabbitmq"
	"queue-manager/common/rabbitmq/channel_pool"
)

// ProducerOptions configures the producer
type ProducerOptions struct {
	PublishTimeout     time.Duration // is how long to wait for a publish operation
	UseConfirms        bool          // determines whether to use publisher confirms
	ConfirmTimeout     time.Duration // is how long to wait for a confirm
	MaxChannelPoolSize int           // is how many channels to maintain in the pool
}

// DefaultProducerOptions provides sensible defaults
func DefaultProducerOptions() ProducerOptions {
	return ProducerOptions{
		PublishTimeout:     rabbitmq.DefaultPublishTimeout,
		UseConfirms:        true,
		ConfirmTimeout:     5 * time.Second,
		MaxChannelPoolSize: 100,
	}
}

// Producer sends messages to queues, with support for consistent hash-based partitioning
// This implements the queue.Producer interface
type Producer struct {
	channelPool    channel_pool.ChannelPool // channel pool for publishing
	exchangeName   string                   // main exchange name
	publishTimeout time.Duration            // is how long to wait for a publish operation
	useConfirms    bool                     // determines whether to use publisher confirms
	confirmTimeout time.Duration            // is how long to wait for a confirm
	logger         *slog.Logger             // logger for the producer
}

// NewProducer creates a producer for a queue with the provided connection
func NewProducer(conn *rabbitmq.Connection, exchangeName string) (queue.Producer, error) {
	return NewProducerWithOptions(conn, exchangeName, DefaultProducerOptions())
}

// NewProducerWithOptions creates a producer with custom options
func NewProducerWithOptions(conn *rabbitmq.Connection, exchangeName string, options ProducerOptions) (queue.Producer, error) {
	return NewProducerWithOptionsAndExchangeSetup(conn, exchangeName, options, topology.NewDefaultExchangeSetup(exchangeName))
}

// NewProducerWithOptionsAndExchangeSetup creates a producer with custom options and exchange setup
func NewProducerWithOptionsAndExchangeSetup(conn *rabbitmq.Connection, exchangeName string, options ProducerOptions, exchangeSetup topology.ExchangeSetup) (queue.Producer, error) {
	logger := slog.With(
		"component", "queue-producer",
		"exchange", exchangeName,
	)

	// Create a thread-safe channel pool with confirms if needed
	poolOptions := channel_pool.DefaultChannelPoolOptions()
	poolOptions.MaxSize = options.MaxChannelPoolSize
	poolOptions.EnableConfirms = options.UseConfirms

	channelPool, err := channel_pool.NewChannelPool(conn, poolOptions)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create channel pool")
	}

	// Setup main exchange (the only real dependency for the producer)
	err = exchangeSetup.SetupMainExchange(conn)
	if err != nil {
		return nil, errors.Wrap(err, "failed to setup main exchange")
	}

	return &Producer{
		channelPool:    channelPool,
		exchangeName:   exchangeName,
		publishTimeout: options.PublishTimeout,
		useConfirms:    options.UseConfirms,
		confirmTimeout: options.ConfirmTimeout,
		logger:         logger,
	}, nil
}

// Publish publishes a message using the specified topic as the routing key
func (p *Producer) Publish(topic string, payload []byte) error {
	return p.publish(topic, "", payload)
}

// PublishWithHashKey publishes a message using a topic and a consistent hash key
func (p *Producer) PublishWithHashKey(topic string, hashKey string, payload []byte) error {
	return p.publish(topic, hashKey, payload)
}

// publish sends a message to the exchange with specified routing key
func (p *Producer) publish(routingKey string, hashKey string, payload []byte) error {
	logger := p.logger.With(
		"operation", "publish",
		"routingKey", routingKey,
		"payloadSize", len(payload),
	)

	// Create a context with a timeout
	ctx, cancel := context.WithTimeout(context.Background(), p.publishTimeout)
	defer cancel()

	logger.Debug("Publishing message to exchange")

	amqpHeaders := amqp.Table{}

	// If a hash key is provided, add it as a header for consistent hash routing
	if hashKey != "" {
		amqpHeaders[rabbitmq.ConsistentHashHeader] = hashKey
	}

	// build the msg
	msg := amqp.Publishing{
		Headers:      amqpHeaders,
		ContentType:  "application/json",
		Body:         payload,
		DeliveryMode: amqp.Persistent, // Make message persistent
		Timestamp:    time.Now(),
	}

	// Get a thread-safe channel from the pool
	channel, err := p.channelPool.GetChannel()
	if err != nil {
		return errors.Wrap(err, "failed to get channel from pool")
	}
	defer p.channelPool.Release(channel)

	// Publish the message
	err = channel.PublishWithContext(
		ctx,
		p.exchangeName, // exchange
		routingKey,     // routing key
		true,           // mandatory - ensure message is routed to a queue
		false,          // immediate
		msg,
	)
	if err != nil {
		logger.Error("Failed to publish message", "error", err)
		return errors.Wrap(err, "failed to publish message")
	}

	logger.Info("Message published successfully")
	return nil
}

// Close releases resources
func (p *Producer) Close() error {
	if p.channelPool != nil {
		p.channelPool.Close()
	}
	return nil
}
