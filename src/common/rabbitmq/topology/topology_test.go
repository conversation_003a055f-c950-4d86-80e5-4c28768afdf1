package topology

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// This file contains tests for the topology package

func TestSharedQueueTopology(t *testing.T) {
	// Create a shared queue topology
	topics := []string{"message.created", "user.updated"}
	topology := NewSharedQueueTopology("events", "notification", topics)

	// Verify basic properties
	assert.Equal(t, "events", topology.GetExchangeName())
	assert.Equal(t, "notification.work", topology.GetQueueName())
	assert.Equal(t, "notification.dlq", topology.GetDLQName())
	assert.Equal(t, topics, topology.GetTopics())
	assert.Nil(t, topology.GetPartitionQueueNames())
	assert.Equal(t, "", topology.GetRouterExchangeName())
}

func TestPartitionedQueueTopology(t *testing.T) {
	// Create a partitioned queue topology
	topics := []string{"message.created", "user.updated"}
	partitionCount := 3
	topology := NewPartitionedQueueDefinition("events", "user-events", partitionCount, topics)

	// Verify basic properties
	assert.Equal(t, "events", topology.GetExchangeName())
	assert.Equal(t, "user-events.work", topology.GetQueueName()) // Base name for retry queues
	assert.Equal(t, "user-events.dlq", topology.GetDLQName())
	assert.Equal(t, topics, topology.GetTopics())

	// Verify partition-specific properties
	expectedPartitions := []string{
		"user-events.work.1",
		"user-events.work.2",
		"user-events.work.3",
	}
	assert.Equal(t, expectedPartitions, topology.GetPartitionQueueNames())
	assert.Equal(t, "user-events_router_exchange", topology.GetRouterExchangeName())
}

func TestSetupDLQ(t *testing.T) {
	// This is now an integration test in topology_integration_test.go
	t.Skip("Integration test moved to topology_integration_test.go")
}

func TestGetRetryQueueName(t *testing.T) {
	// Test single retry queue mode (default)
	t.Run("Single retry queue mode", func(t *testing.T) {
		// Create a base topology for testing
		topics := []string{"message.created"}
		topology := NewBaseTopology("main_exchange", "notification_work_queue", topics, "notification_work_queue")

		// In single retry queue mode, all TTL values should return the same queue name
		testCases := []struct {
			name     string
			ttlMs    int64
			expected string
		}{
			{"Less than 1 second", 500, "notification_work_queue.retry"},
			{"1 second", 1000, "notification_work_queue.retry"},
			{"5 seconds", 5000, "notification_work_queue.retry"},
			{"30 seconds", 30000, "notification_work_queue.retry"},
			{"1 minute", 60000, "notification_work_queue.retry"},
			{"5 minutes", 300000, "notification_work_queue.retry"},
			{"30 minutes", 1800000, "notification_work_queue.retry"},
			{"1 hour", 3600000, "notification_work_queue.retry"},
			{"2 hours", 7200000, "notification_work_queue.retry"},
		}

		// Run test cases
		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				result := topology.GetRetryQueueName(tc.ttlMs)
				assert.Equal(t, tc.expected, result)
			})
		}
	})

	// Test multiple retry queues mode
	t.Run("Multiple retry queues mode", func(t *testing.T) {
		// Create a base topology for testing
		topics := []string{"message.created"}
		topology := NewBaseTopology("main_exchange", "notification_work_queue", topics, "notification_work_queue")

		// Enable multiple retry queues mode
		topology.EnableMultipleRetryQueues()

		// Test different TTL values with multiple retry queues enabled
		testCases := []struct {
			name     string
			ttlMs    int64
			expected string
		}{
			{"Less than 1 second", 500, "notification_work_queue.retry.500ms"},
			{"1 second", 1000, "notification_work_queue.retry.1.0s"},
			{"5 seconds", 5000, "notification_work_queue.retry.5.0s"},
			{"30 seconds", 30000, "notification_work_queue.retry.30.0s"},
			{"1 minute", 60000, "notification_work_queue.retry.1.0m"},
			{"5 minutes", 300000, "notification_work_queue.retry.5.0m"},
			{"30 minutes", 1800000, "notification_work_queue.retry.30.0m"},
			{"1 hour", 3600000, "notification_work_queue.retry.1.0h"},
			{"2 hours", 7200000, "notification_work_queue.retry.2.0h"},
		}

		// Run test cases
		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				result := topology.GetRetryQueueName(tc.ttlMs)
				assert.Equal(t, tc.expected, result)
			})
		}
	})
}
