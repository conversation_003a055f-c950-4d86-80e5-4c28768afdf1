package topology

import (
	"log/slog"

	"github.com/cockroachdb/errors"
	"queue-manager/common/rabbitmq"
)

// ExchangeSetup defines an interface for setting up exchanges
type ExchangeSetup interface {
	SetupMainExchange(connection *rabbitmq.Connection) error
}

// Logger for the rabbitmq package
var packageLogger = slog.With("component", "topology")

type DefaultExchangeSetup struct {
	exchangeName string
}

func NewDefaultExchangeSetup(exchangeName string) ExchangeSetup {
	return &DefaultExchangeSetup{exchangeName: exchangeName}
}

func (e *DefaultExchangeSetup) SetupMainExchange(connection *rabbitmq.Connection) error {
	// Create a channel for exchange setup
	channel, err := connection.Channel()
	if err != nil {
		slog.Error("Failed to create channel for exchange setup", "error", err)
		return errors.Wrapf(err, "failed to create channel for exchange setup")
	}
	defer channel.Close()

	// Declare the working exchange
	slog.Debug("Declaring working exchange", "exchange", e.exchangeName)
	if err := channel.ExchangeDeclare(
		e.exchangeName, // name
		"topic",        // type
		true,           // durable
		false,          // auto-deleted
		false,          // internal
		false,          // no-wait
		nil,            // arguments
	); err != nil {
		slog.Error("Failed to declare working exchange", "error", err)
		return errors.Wrapf(err, "failed to declare working exchange '%s'", e.exchangeName)
	}

	return nil
}
