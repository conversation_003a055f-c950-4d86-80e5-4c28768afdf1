package topology

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"log/slog"
	"queue-manager/common/queue"
	"queue-manager/common/rabbitmq"

	"github.com/cockroachdb/errors"
	amqp "github.com/rabbitmq/amqp091-go"
)

// BaseTopology provides common functionality for all topology types
type BaseTopology struct {
	// Basic attributes
	exchangeName          string
	queuesBaseName        string
	workQueueName         string
	dlqName               string
	topics                []string
	retryDLRoutingKey     string // retry dead letter routing key (where do the messages go after the retry TTL expires)
	multipleRetriesQueues bool

	// Logger
	logger *slog.Logger
}

// NewBaseTopology creates a base topology with common fields
func NewBaseTopology(exchangeName string, queuesBaseName string, topics []string, retryDLRoutingKey string) *BaseTopology {
	workQueueName := fmt.Sprintf("%s.work", queuesBaseName)
	dlqName := fmt.Sprintf("%s.dlq", queuesBaseName)

	if retryDLRoutingKey == "" {
		retryDLRoutingKey = workQueueName
	}

	return &BaseTopology{
		exchangeName:          exchangeName,
		queuesBaseName:        queuesBaseName,
		workQueueName:         workQueueName,
		dlqName:               dlqName,
		topics:                topics,
		retryDLRoutingKey:     retryDLRoutingKey,
		multipleRetriesQueues: false,
		logger:                packageLogger.With("topology", "base"),
	}
}

func (b *BaseTopology) GetExchangeName() string {
	return b.exchangeName
}

func (b *BaseTopology) GetQueueName() string {
	return b.workQueueName
}

func (b *BaseTopology) GetDLQName() string {
	return b.dlqName
}

func (b *BaseTopology) GetTopics() []string {
	return b.topics
}

func (b *BaseTopology) GetPartitionQueueNames() []string {
	return nil
}

func (b *BaseTopology) GetRouterExchangeName() string {
	return ""
}

// EnableMultipleRetryQueues enables the multiple retry queues mode
// This creates separate queues for each TTL value instead of using a single retry queue
func (b *BaseTopology) EnableMultipleRetryQueues() {
	b.multipleRetriesQueues = true
}

// Setup is implemented by derived topologies
func (b *BaseTopology) Setup(connection *rabbitmq.Connection) error {
	return errors.New("Setup must be implemented by derived topology")
}

// SetupDLQ sets up the dead letter queue for any topology
func (b *BaseTopology) SetupDLQ(connection *rabbitmq.Connection) error {
	dlqLogger := b.logger.With(
		"operation", "SetupDLQ",
		"dlq", b.dlqName,
	)

	// Create a channel for DLQ setup
	channel, err := connection.Channel()
	if err != nil {
		dlqLogger.Error("Failed to create channel for DLQ setup", "error", err)
		return errors.Wrap(err, "failed to create channel for DLQ setup")
	}
	defer channel.Close()

	// Declare the dead letter queue (no DLX, final destination)
	dlqLogger.Debug("Declaring DLQ queue")
	if _, err := channel.QueueDeclare(
		b.dlqName, // name
		true,      // durable
		false,     // delete when unused
		false,     // exclusive
		false,     // no-wait
		amqp.Table{
			"x-queue-type": "quorum",
		},
	); err != nil {
		dlqLogger.Error("Failed to declare DLQ", "error", err)
		return errors.Wrapf(err, "failed to declare DLQ '%s'", b.dlqName)
	}

	if err := channel.QueueBind(
		b.dlqName,      // queue name
		b.dlqName,      // routing key
		b.exchangeName, // exchange
		false,          // no-wait
		nil,            // arguments
	); err != nil {
		dlqLogger.Error("Failed to bind DLQ", "error", err)
		return errors.Wrapf(err, "failed to bind DLQ '%s' to exchange '%s'",
			b.dlqName, b.exchangeName)
	}

	dlqLogger.Info("Successfully set up DLQ queue")
	return nil
}

func (b *BaseTopology) GetRetryQueueName(ttlMs int64) string {
	if b.multipleRetriesQueues {
		return b.getRetryQueueNameMultiple(ttlMs)
	} else {
		return b.getRetryQueueNameSingle()
	}
}

func (b *BaseTopology) SetupRetryQueues(connection *rabbitmq.Connection, retryPolicy queue.RetryPolicy) error {
	if b.multipleRetriesQueues {
		return b.setupRetryQueuesMultiple(connection, retryPolicy)
	} else {
		return b.setupRetryQueuesSingle(connection, retryPolicy)
	}
}

// SetupRetryQueues creates all retry queues based on the retry policy
func (b *BaseTopology) setupRetryQueuesMultiple(connection *rabbitmq.Connection, retryPolicy queue.RetryPolicy) error {
	retryLogger := b.logger.With(
		"operation", "SetupRetryQueues",
		"maxRetries", retryPolicy.GetMaxRetries(),
	)

	retryLogger.Info("Setting up retry queues")

	// Create a channel for retry queues setup
	channel, err := connection.Channel()
	if err != nil {
		retryLogger.Error("Failed to create channel for retry queues setup", "error", err)
		return errors.Wrap(err, "failed to create channel for retry queues setup")
	}
	defer channel.Close()

	// Create a retry queue for each backoff interval
	var allTTLs []int64
	for i := 0; i < retryPolicy.GetMaxRetries(); i++ {
		ttlMs := retryPolicy.NextTTL(i)
		allTTLs = append(allTTLs, ttlMs)
	}

	// Create each unique retry queue
	seen := make(map[int64]bool)
	for _, ttlMs := range allTTLs {
		if seen[ttlMs] {
			continue // Skip if we've already created this TTL queue
		}
		seen[ttlMs] = true

		retryQueueName := b.GetRetryQueueName(ttlMs)
		queueLogger := retryLogger.With(
			"retryQueue", retryQueueName,
			"ttlMs", ttlMs,
		)

		queueLogger.Debug("Creating retry queue")

		// Declare the retry queue with the specific TTL
		args := amqp.Table{
			"x-queue-type":              "quorum",
			"x-dead-letter-exchange":    b.exchangeName,
			"x-dead-letter-routing-key": b.retryDLRoutingKey,
			"x-message-ttl":             ttlMs,
		}

		_, err := channel.QueueDeclare(
			retryQueueName, // Name
			true,           // Durable
			false,          // Auto-delete
			false,          // Exclusive
			false,          // No-wait
			args,           // Arguments
		)

		if err != nil {
			queueLogger.Error("Failed to declare retry queue", "error", err)
			return errors.Wrapf(err, "failed to declare retry queue '%s'", retryQueueName)
		}

		queueLogger.Info("Created retry queue")
	}

	retryLogger.Info("Successfully set up all retry queues")
	return nil
}

func (b *BaseTopology) getRetryQueueNameMultiple(ttlMs int64) string {
	if ttlMs < 1000 {
		return fmt.Sprintf("%s.retry.%dms", b.queuesBaseName, ttlMs)
	} else if ttlMs < 60000 {
		seconds := float64(ttlMs) / 1000
		return fmt.Sprintf("%s.retry.%.1fs", b.queuesBaseName, seconds)
	} else if ttlMs < 3600000 {
		minutes := float64(ttlMs) / 60000
		return fmt.Sprintf("%s.retry.%.1fm", b.queuesBaseName, minutes)
	} else {
		hours := float64(ttlMs) / 3600000
		return fmt.Sprintf("%s.retry.%.1fh", b.queuesBaseName, hours)
	}
}

// GetRetryQueueName returns the name of the single retry queue
func (b *BaseTopology) getRetryQueueNameSingle() string {
	// Now we simply return the single retry queue name
	// The ttlMs parameter is kept for backward compatibility
	return fmt.Sprintf("%s.retry", b.queuesBaseName)
}

// SetupRetryQueues creates a single retry queue
func (b *BaseTopology) setupRetryQueuesSingle(connection *rabbitmq.Connection, retryPolicy queue.RetryPolicy) error {
	retryLogger := b.logger.With(
		"operation", "SetupRetryQueues",
		"maxRetries", retryPolicy.GetMaxRetries(),
	)

	retryLogger.Info("Setting up single retry queue")

	// Create a channel for retry queue setup
	channel, err := connection.Channel()
	if err != nil {
		retryLogger.Error("Failed to create channel for retry queue setup", "error", err)
		return errors.Wrap(err, "failed to create channel for retry queue setup")
	}
	defer channel.Close()

	// Create a single retry queue that doesn't have a fixed TTL
	// TTL will be set per message instead
	retryQueueName := b.getRetryQueueNameSingle()
	queueLogger := retryLogger.With(
		"retryQueue", retryQueueName,
	)

	queueLogger.Debug("Creating retry queue")

	// Declare the retry queue without a fixed TTL
	args := amqp.Table{
		"x-queue-type":              "quorum",
		"x-dead-letter-exchange":    b.exchangeName,
		"x-dead-letter-routing-key": b.retryDLRoutingKey,
	}

	_, err = channel.QueueDeclare(
		retryQueueName, // Name
		true,           // Durable
		false,          // Auto-delete
		false,          // Exclusive
		false,          // No-wait
		args,           // Arguments
	)

	if err != nil {
		queueLogger.Error("Failed to declare retry queue", "error", err)
		return errors.Wrapf(err, "failed to declare retry queue '%s'", retryQueueName)
	}

	queueLogger.Info("Created retry queue")
	retryLogger.Info("Successfully set up single retry queue")
	return nil
}

// Binding represents a RabbitMQ binding from the Management API
type Binding struct {
	Source          string                 `json:"source"`
	Destination     string                 `json:"destination"`
	DestinationType string                 `json:"destination_type"`
	RoutingKey      string                 `json:"routing_key"`
	Arguments       map[string]interface{} `json:"arguments"`
}

// RemoveObsoleteTopicBindings removes topic bindings that are no longer needed
// This is a common implementation that can be used by both shared and partitioned topologies
func (b *BaseTopology) RemoveObsoleteTopicBindings(
	channel *amqp.Channel,
	logger *slog.Logger,
	currentTopics []string,
	bindingTarget string, // queue name for shared, exchange name for partitioned
	isExchangeBinding bool, // true for exchange-to-exchange, false for exchange-to-queue
) error {
	topicLogger := logger.With("operation", "removeObsoleteTopicBindings")

	// Get current bindings
	currentBindings, err := b.getCurrentBindingsViaAPI(bindingTarget, isExchangeBinding)
	if err != nil {
		topicLogger.Debug("Failed to get current topic bindings, assuming no existing bindings", "error", err)
		// Fallback: assume no existing bindings and let RabbitMQ handle duplicates
		currentBindings = []string{}
	}

	// Convert current topics to a set for easier comparison
	newTopicsSet := make(map[string]bool)
	for _, topic := range currentTopics {
		newTopicsSet[topic] = true
	}

	// Find topics to remove (exist in current bindings but not in new topics)
	var topicsToRemove []string
	for _, binding := range currentBindings {
		// Don't remove the main binding (queue name or exchange name as routing key)
		if !newTopicsSet[binding] && binding != bindingTarget {
			topicsToRemove = append(topicsToRemove, binding)
		}
	}

	// Remove obsolete topic bindings
	for _, topic := range topicsToRemove {
		topicLogger.Info("Removing obsolete topic binding", "topic", topic)

		var err error
		if isExchangeBinding {
			// Exchange to exchange binding
			err = channel.ExchangeUnbind(
				bindingTarget,  // destination exchange
				topic,          // routing key
				b.exchangeName, // source exchange
				false,          // no-wait
				nil,            // arguments
			)
		} else {
			// Exchange to queue binding
			err = channel.QueueUnbind(
				bindingTarget,  // queue name
				topic,          // routing key
				b.exchangeName, // exchange
				nil,            // arguments
			)
		}

		if err != nil {
			topicLogger.Error("Failed to unbind obsolete topic", "topic", topic, "error", err)
			return errors.Wrapf(err, "failed to unbind obsolete topic '%s'", topic)
		}
	}

	// Log results
	if len(topicsToRemove) > 0 {
		topicLogger.Info("Removed obsolete topic bindings", "removed", topicsToRemove)
	} else {
		topicLogger.Debug("No obsolete topics found")
	}

	return nil
}

// getCurrentBindingsViaAPI uses LavinMQ Management API to get current bindings
func (b *BaseTopology) getCurrentBindingsViaAPI(bindingTarget string, isExchangeBinding bool) ([]string, error) {
	// Get Management API configuration from environment
	managementHost := os.Getenv("RABBITMQ_HOST")
	if managementHost == "" {
		managementHost = "localhost"
	}

	managementPort := "15672" // LavinMQ management port is fixed

	username := os.Getenv("RABBITMQ_USER")
	if username == "" {
		username = "guest"
	}

	password := os.Getenv("RABBITMQ_PASS")
	if password == "" {
		password = "guest"
	}

	vhost := os.Getenv("RABBITMQ_VHOST")
	if vhost == "" {
		vhost = "/"
	}

	// URL encode vhost for API call
	encodedVhost := strings.ReplaceAll(vhost, "/", "%2F")

	// Build API URL based on binding type
	var apiURL string
	if isExchangeBinding {
		// Exchange-to-exchange bindings
		apiURL = fmt.Sprintf("http://%s:%s/api/bindings/%s/e/%s/e/%s",
			managementHost, managementPort, encodedVhost, b.exchangeName, bindingTarget)
	} else {
		// Exchange-to-queue bindings
		apiURL = fmt.Sprintf("http://%s:%s/api/bindings/%s/e/%s/q/%s",
			managementHost, managementPort, encodedVhost, b.exchangeName, bindingTarget)
	}

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Create request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set basic auth
	req.SetBasicAuth(username, password)
	req.Header.Set("Accept", "application/json")

	// Make request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to call management API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("management API returned status %d", resp.StatusCode)
	}

	// Parse response
	var bindings []Binding
	if err := json.NewDecoder(resp.Body).Decode(&bindings); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Extract routing keys from bindings
	var topics []string
	for _, binding := range bindings {
		topics = append(topics, binding.RoutingKey)
	}

	return topics, nil
}
