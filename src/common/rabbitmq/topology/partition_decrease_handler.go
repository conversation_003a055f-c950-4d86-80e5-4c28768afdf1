package topology

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.com/cockroachdb/errors"
	amqp "github.com/rabbitmq/amqp091-go"

	"queue-manager/common/rabbitmq"
)

// PartitionDecreaseHandler handles the process of reducing partition count
type PartitionDecreaseHandler struct {
	queuesBaseName     string
	routerExchangeName string
}

// NewPartitionDecreaseHandler creates a new partition decrease handler
func NewPartitionDecreaseHandler(queuesBaseName, routerExchangeName string) *PartitionDecreaseHandler {
	return &PartitionDecreaseHandler{
		queuesBaseName:     queuesBaseName,
		routerExchangeName: routerExchangeName,
	}
}

// HandlePartitionDecrease manages the process of reducing partition count
func (h *PartitionDecreaseHandler) HandlePartitionDecrease(conn *rabbitmq.Connection, desiredPartitionCount int, logger *slog.Logger) error {
	decreaseLogger := logger.With("operation", "handlePartitionDecrease")

	// For now, we'll use a conservative approach that doesn't risk closing channels
	// We'll only attempt to remove queues that we know should exist based on naming patterns
	// This avoids the problematic queue existence checking that can close channels

	decreaseLogger.Debug("Checking for partition decrease", "desiredCount", desiredPartitionCount)

	// Instead of checking if queues exist, we'll attempt to process a reasonable range
	// of potential excess queues. The processQueueForRemoval method should handle
	// non-existent queues gracefully.
	maxPartitionsToCheck := desiredPartitionCount + 5 // Conservative range
	var potentialQueuesToRemove []string

	for i := desiredPartitionCount + 1; i <= maxPartitionsToCheck; i++ {
		queueName := fmt.Sprintf("%s.work.%d", h.queuesBaseName, i)
		potentialQueuesToRemove = append(potentialQueuesToRemove, queueName)
	}

	if len(potentialQueuesToRemove) > 0 {
		decreaseLogger.Debug("Attempting to clean up potential excess queues",
			"desiredCount", desiredPartitionCount,
			"potentialQueues", potentialQueuesToRemove)

		// Process each potential queue for removal
		// The processQueueForRemoval method should handle non-existent queues gracefully
		removedCount := 0
		for _, queueName := range potentialQueuesToRemove {
			if err := h.processQueueForRemoval(conn, queueName, decreaseLogger); err != nil {
				// Log error but continue with other queues
				decreaseLogger.Debug("Queue removal attempt failed (queue may not exist)", "queue", queueName, "error", err)
			} else {
				removedCount++
				decreaseLogger.Debug("Successfully processed queue for removal", "queue", queueName)
			}
		}

		if removedCount > 0 {
			decreaseLogger.Info("Successfully completed partition decrease",
				"removedQueues", removedCount)
		} else {
			decreaseLogger.Debug("No queues were removed (none existed)")
		}
	} else {
		decreaseLogger.Debug("No partition decrease needed")
	}

	return nil
}

// processQueueForRemoval handles the complete removal process for a single queue
func (h *PartitionDecreaseHandler) processQueueForRemoval(conn *rabbitmq.Connection, queueName string, logger *slog.Logger) error {
	removalLogger := logger.With("operation", "processQueueForRemoval", "queue", queueName)

	removalLogger.Info("Starting queue removal process")

	// Create a dedicated channel for this queue removal to avoid channel closure issues
	channel, err := conn.Channel()
	if err != nil {
		removalLogger.Error("Failed to create channel for queue removal", "error", err)
		return errors.Wrap(err, "failed to create channel for queue removal")
	}
	defer channel.Close()

	// Step 1: Unbind the queue from the router exchange
	if err := h.unbindQueueFromRouter(channel, queueName, removalLogger); err != nil {
		return errors.Wrapf(err, "failed to unbind queue '%s' from router exchange", queueName)
	}

	// Step 2: Move existing messages back to router exchange
	if err := h.moveMessagesToRouter(channel, queueName, removalLogger); err != nil {
		return errors.Wrapf(err, "failed to move messages from queue '%s' to router exchange", queueName)
	}

	// Step 3: Delete the queue
	if err := h.deleteQueue(channel, queueName, removalLogger); err != nil {
		return errors.Wrapf(err, "failed to delete queue '%s'", queueName)
	}

	removalLogger.Info("Successfully completed queue removal")
	return nil
}

// unbindQueueFromRouter removes the binding between the queue and router exchange
func (h *PartitionDecreaseHandler) unbindQueueFromRouter(channel *amqp.Channel, queueName string, logger *slog.Logger) error {
	unbindLogger := logger.With("operation", "unbindQueueFromRouter")

	unbindLogger.Debug("Unbinding queue from router exchange",
		"routerExchange", h.routerExchangeName,
		"routingKey", "500")

	// Unbind the queue from the router exchange
	// Note: We use the same routing key ("500") that was used during binding
	if err := channel.QueueUnbind(
		queueName,            // queue name
		"500",                // routing key (same as used in binding)
		h.routerExchangeName, // exchange
		nil,                  // arguments
	); err != nil {
		// If the queue doesn't exist, that's fine - it's already unbound
		errStr := err.Error()
		if strings.Contains(errStr, "NOT_FOUND") || strings.Contains(errStr, "404") {
			unbindLogger.Debug("Queue does not exist, already unbound")
			return nil
		}
		unbindLogger.Error("Failed to unbind queue from router exchange", "error", err)
		return errors.Wrapf(err, "failed to unbind queue '%s' from router exchange '%s'",
			queueName, h.routerExchangeName)
	}

	unbindLogger.Info("Successfully unbound queue from router exchange")
	return nil
}

// moveMessagesToRouter moves all messages from a queue back to the router exchange for redistribution
func (h *PartitionDecreaseHandler) moveMessagesToRouter(channel *amqp.Channel, queueName string, logger *slog.Logger) error {
	moveLogger := logger.With("operation", "moveMessagesToRouter")

	moveLogger.Debug("Starting message movement from queue to router exchange")

	// Try to get queue info to check message count using QueueDeclarePassive
	// This operation can close the channel if the queue doesn't exist, so we handle it carefully
	queueInfo, err := channel.QueueDeclarePassive(
		queueName, // name
		true,      // durable
		false,     // delete when unused
		false,     // exclusive
		false,     // no-wait
		nil,       // arguments
	)
	if err != nil {
		// If the queue doesn't exist, that's fine - nothing to move
		errStr := err.Error()
		if strings.Contains(errStr, "NOT_FOUND") || strings.Contains(errStr, "404") {
			moveLogger.Debug("Queue does not exist, nothing to move")
			return nil
		}
		// If we get a channel closure error, it means the queue doesn't exist
		if strings.Contains(errStr, "channel/connection is not open") || strings.Contains(errStr, "504") {
			moveLogger.Debug("Queue does not exist (channel closed), nothing to move")
			return nil
		}
		moveLogger.Error("Failed to inspect queue for message count", "error", err)
		return errors.Wrapf(err, "failed to inspect queue '%s'", queueName)
	}

	messageCount := queueInfo.Messages
	moveLogger.Info("Queue message count", "messageCount", messageCount)

	if messageCount == 0 {
		moveLogger.Debug("No messages to move")
		return nil
	}

	// Set up a consumer to read messages from the queue
	msgs, err := channel.Consume(
		queueName, // queue
		"",        // consumer tag (auto-generated)
		false,     // auto-ack (we'll ack manually after republishing)
		false,     // exclusive
		false,     // no-local
		false,     // no-wait
		nil,       // args
	)
	if err != nil {
		moveLogger.Error("Failed to start consuming from queue", "error", err)
		return errors.Wrapf(err, "failed to start consuming from queue '%s'", queueName)
	}

	// Process messages with a timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	movedCount := 0

	moveLogger.Debug("Starting message processing loop")

	for {
		select {
		case msg, ok := <-msgs:
			if !ok {
				moveLogger.Debug("Message channel closed")
				goto done
			}

			if err := h.republishMessage(channel, msg, moveLogger); err != nil {
				moveLogger.Error("Failed to republish message", "error", err)
				// Reject the message without requeue since we're removing the queue anyway
				if ackErr := msg.Reject(false); ackErr != nil {
					moveLogger.Error("Failed to reject message", "error", ackErr)
				}
				continue
			}

			// Acknowledge the original message
			if ackErr := msg.Ack(false); ackErr != nil {
				moveLogger.Error("Failed to acknowledge message", "error", ackErr)
			} else {
				movedCount++
			}

		case <-ctx.Done():
			moveLogger.Warn("Message movement timed out", "movedCount", movedCount, "expectedCount", messageCount)
			goto done
		}

		// Check if we've moved all messages
		if movedCount >= messageCount {
			moveLogger.Debug("All messages processed")
			goto done
		}
	}

done:
	// Cancel the consumer
	if err := channel.Cancel("", false); err != nil {
		moveLogger.Error("Failed to cancel consumer", "error", err)
	}

	moveLogger.Info("Completed message movement",
		"movedCount", movedCount,
		"expectedCount", messageCount)

	return nil
}

// republishMessage republishes a single message to the router exchange
func (h *PartitionDecreaseHandler) republishMessage(channel *amqp.Channel, msg amqp.Delivery, logger *slog.Logger) error {
	republishLogger := logger.With("operation", "republishMessage")

	// Create a new publishing with the original message data
	publishing := amqp.Publishing{
		Headers:         msg.Headers,
		ContentType:     msg.ContentType,
		ContentEncoding: msg.ContentEncoding,
		DeliveryMode:    msg.DeliveryMode,
		Priority:        msg.Priority,
		CorrelationId:   msg.CorrelationId,
		ReplyTo:         msg.ReplyTo,
		Expiration:      msg.Expiration,
		MessageId:       msg.MessageId,
		Timestamp:       msg.Timestamp,
		Type:            msg.Type,
		UserId:          msg.UserId,
		AppId:           msg.AppId,
		Body:            msg.Body,
	}

	// Determine the routing key to use
	// If the message has an original routing key header, use that
	// Otherwise, use the router exchange name as routing key
	routingKey := h.routerExchangeName
	if originalKey, exists := msg.Headers[rabbitmq.OriginalRoutingKey]; exists {
		if keyStr, ok := originalKey.(string); ok {
			routingKey = keyStr
		}
	}

	republishLogger.Debug("Republishing message to router exchange",
		"routerExchange", h.routerExchangeName,
		"routingKey", routingKey)

	// Publish to the router exchange
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := channel.PublishWithContext(
		ctx,
		h.routerExchangeName, // exchange
		routingKey,           // routing key
		false,                // mandatory
		false,                // immediate
		publishing,
	); err != nil {
		republishLogger.Error("Failed to republish message", "error", err)
		return errors.Wrap(err, "failed to republish message to router exchange")
	}

	republishLogger.Debug("Successfully republished message")
	return nil
}

// deleteQueue removes the queue from RabbitMQ
func (h *PartitionDecreaseHandler) deleteQueue(channel *amqp.Channel, queueName string, logger *slog.Logger) error {
	deleteLogger := logger.With("operation", "deleteQueue")

	deleteLogger.Debug("Deleting queue")

	// Delete the queue
	// ifUnused: false - delete even if there are consumers
	// ifEmpty: false - delete even if there are messages (we should have moved them already)
	_, err := channel.QueueDelete(
		queueName, // queue name
		false,     // ifUnused
		false,     // ifEmpty
		false,     // noWait
	)
	if err != nil {
		// If the queue doesn't exist, that's fine - it's already "deleted"
		errStr := err.Error()
		if strings.Contains(errStr, "NOT_FOUND") || strings.Contains(errStr, "404") {
			deleteLogger.Debug("Queue does not exist, already deleted")
			return nil
		}
		// If we get a channel closure error, it likely means the queue doesn't exist
		if strings.Contains(errStr, "channel/connection is not open") || strings.Contains(errStr, "504") {
			deleteLogger.Debug("Queue does not exist (channel closed), already deleted")
			return nil
		}
		deleteLogger.Error("Failed to delete queue", "error", err)
		return errors.Wrapf(err, "failed to delete queue '%s'", queueName)
	}

	deleteLogger.Info("Successfully deleted queue")
	return nil
}
