package topology

import (
	"queue-manager/common/queue"
	"queue-manager/common/rabbitmq"
)

type Topology interface {
	// Basic topology operations
	Setup(connection *rabbitmq.Connection) error
	SetupDLQ(connection *rabbitmq.Connection) error
	SetupRetryQueues(connection *rabbitmq.Connection, retryPolicy queue.RetryPolicy) error
	GetRetryQueueName(ttlMs int64) string

	// Queue information getters
	GetExchangeName() string
	GetQueueName() string
	GetDLQName() string
	GetTopics() []string

	// For partitioned queues
	GetPartitionQueueNames() []string
	GetRouterExchangeName() string
}
