package topology

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/docker/go-connections/nat"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"

	amqp "github.com/rabbitmq/amqp091-go"
	"queue-manager/common/queue"
	"queue-manager/common/rabbitmq"
)

// ExponentialBackoffRetryPolicy implements a simple exponential backoff retry policy for testing
type ExponentialBackoffRetryPolicy struct {
	maxRetries int
	baseMs     int64
	factor     float64
}

func NewExponentialBackoffRetryPolicy(maxRetries int, baseMs int64, factor float64) queue.RetryPolicy {
	return &ExponentialBackoffRetryPolicy{
		maxRetries: maxRetries,
		baseMs:     baseMs,
		factor:     factor,
	}
}

func (e *ExponentialBackoffRetryPolicy) NextTTL(retryCount int) int64 {
	if retryCount >= e.maxRetries {
		return 0
	}

	// Calculate exponential backoff
	ttl := e.baseMs
	for i := 0; i < retryCount; i++ {
		ttl = int64(float64(ttl) * e.factor)
	}
	return ttl
}

func (e *ExponentialBackoffRetryPolicy) GetMaxRetries() int {
	return e.maxRetries
}

// startRabbitmqContainer starts a LavinMQ container for testing (supports x-consistent-hash)
func startRabbitmqContainer(ctx context.Context) (testcontainers.Container, rabbitmq.ConnectionConfig, error) {
	rabbitPort := "5672/tcp"
	req := testcontainers.ContainerRequest{
		Image:        "cloudamqp/lavinmq:latest",
		ExposedPorts: []string{rabbitPort},
		WaitingFor:   wait.ForListeningPort(nat.Port(rabbitPort)),
		Env: map[string]string{
			"LAVINMQ_DEFAULT_USER": "guest",
			"LAVINMQ_DEFAULT_PASS": "guest",
		},
	}

	container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	if err != nil {
		return nil, rabbitmq.ConnectionConfig{}, err
	}

	// Get the mapped port
	mappedPort, err := container.MappedPort(ctx, nat.Port(rabbitPort))
	if err != nil {
		return nil, rabbitmq.ConnectionConfig{}, err
	}

	// Get the host
	host, err := container.Host(ctx)
	if err != nil {
		return nil, rabbitmq.ConnectionConfig{}, err
	}

	// Create connection config
	connectionConfig := rabbitmq.ConnectionConfig{
		Name:     "integration-test",
		Username: "guest",
		Password: "guest",
		Host:     host,
		Port:     mappedPort.Port(),
		VHost:    "/",
	}

	return container, connectionConfig, nil
}

// TestSetupDLQIntegration tests the SetupDLQ method with a real RabbitMQ instance
func TestSetupDLQIntegration(t *testing.T) {
	// Skip if not running integration tests
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Set up test environment
	ctx := context.Background()

	// Start RabbitMQ container
	rabbitmqContainer, connectionConfig, err := startRabbitmqContainer(ctx)
	require.NoError(t, err)
	defer func() {
		if err := rabbitmqContainer.Terminate(ctx); err != nil {
			t.Logf("Failed to terminate container: %v", err)
		}
	}()

	// Create connection
	conn, err := rabbitmq.NewConnection(connectionConfig)
	require.NoError(t, err)
	defer conn.Close()

	// Create channel
	channel, err := conn.Channel()
	require.NoError(t, err)
	defer channel.Close()

	// Create exchange
	exchangeName := "test-exchange"
	err = channel.ExchangeDeclare(
		exchangeName, // name
		"topic",      // type
		true,         // durable
		false,        // auto-deleted
		false,        // internal
		false,        // no-wait
		nil,          // arguments
	)
	require.NoError(t, err)

	// Create a base topology for testing
	topics := []string{"message.created"}
	topology := NewBaseTopology(exchangeName, "notification_work_queue", topics, "notification_work_queue")

	// Call the method under test
	err = topology.SetupDLQ(conn)
	require.NoError(t, err)

	// Verify the DLQ was created
	expectedDLQName := topology.GetDLQName()
	queue, err := channel.QueueDeclarePassive(
		expectedDLQName, // name
		true,            // durable
		false,           // delete when unused
		false,           // exclusive
		false,           // no-wait
		nil,             // arguments
	)
	require.NoError(t, err)
	assert.Equal(t, expectedDLQName, queue.Name)

	// Verify the binding was created by publishing a message to the exchange with the DLQ routing key
	// and then consuming from the DLQ
	err = channel.PublishWithContext(
		ctx,
		exchangeName,    // exchange
		expectedDLQName, // routing key
		false,           // mandatory
		false,           // immediate
		amqp.Publishing{
			ContentType: "text/plain",
			Body:        []byte("test message"),
		},
	)
	require.NoError(t, err)

	// Wait a moment for the message to be routed
	time.Sleep(100 * time.Millisecond)

	// Consume the message from the DLQ
	msgs, err := channel.Consume(
		expectedDLQName, // queue
		"",              // consumer
		true,            // auto-ack
		false,           // exclusive
		false,            // no-local
		false,           // no-wait
		nil,             // args
	)
	require.NoError(t, err)

	// Wait for the message
	select {
	case msg := <-msgs:
		assert.Equal(t, "test message", string(msg.Body))
	case <-time.After(1 * time.Second):
		t.Fatal("Timed out waiting for message")
	}
}

// TestSetupRetryQueuesIntegration tests the SetupRetryQueues method with a real RabbitMQ instance
func TestSetupRetryQueuesIntegration(t *testing.T) {
	// Skip if not running integration tests
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Set up test environment
	ctx := context.Background()

	// Start RabbitMQ container
	rabbitmqContainer, connectionConfig, err := startRabbitmqContainer(ctx)
	require.NoError(t, err)
	defer func() {
		if err := rabbitmqContainer.Terminate(ctx); err != nil {
			t.Logf("Failed to terminate container: %v", err)
		}
	}()

	// Create connection
	conn, err := rabbitmq.NewConnection(connectionConfig)
	require.NoError(t, err)
	defer conn.Close()

	// Create channel
	channel, err := conn.Channel()
	require.NoError(t, err)
	defer channel.Close()

	// Create exchange
	exchangeName := "test-exchange"
	err = channel.ExchangeDeclare(
		exchangeName, // name
		"topic",      // type
		true,         // durable
		false,        // auto-deleted
		false,        // internal
		false,        // no-wait
		nil,          // arguments
	)
	require.NoError(t, err)

	// Create a base topology for testing
	topics := []string{"message.created"}
	topology := NewBaseTopology(exchangeName, "notification_work_queue", topics, "notification_work_queue")

	// Enable multiple retry queues mode for this test
	topology.EnableMultipleRetryQueues()

	// Create a retry policy
	retryPolicy := NewExponentialBackoffRetryPolicy(3, 1000, 2.0)

	// Call the method under test
	err = topology.SetupRetryQueues(conn, retryPolicy)
	require.NoError(t, err)

	// Verify the retry queues were created
	expectedRetryQueues := []string{
		"notification_work_queue.retry.1.0s",
		"notification_work_queue.retry.2.0s",
		"notification_work_queue.retry.4.0s",
	}

	for _, queueName := range expectedRetryQueues {
		// Verify the queue exists
		queue, err := channel.QueueDeclarePassive(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		require.NoError(t, err)
		assert.Equal(t, queueName, queue.Name)
	}

	// Test the retry flow by publishing a message to a retry queue and verifying it gets routed
	// to the work queue after the TTL expires

	// First, declare the work queue so we can consume from it
	_, err = channel.QueueDeclare(
		"notification_work_queue", // name
		true,                      // durable
		false,                     // delete when unused
		false,                     // exclusive
		false,                     // no-wait
		amqp.Table{
			"x-queue-type": "quorum",
		},
	)
	require.NoError(t, err)

	// Bind the work queue to the exchange
	err = channel.QueueBind(
		"notification_work_queue", // queue name
		"notification_work_queue", // routing key
		exchangeName,              // exchange
		false,                     // no-wait
		nil,                       // arguments
	)
	require.NoError(t, err)

	// Publish a message to the shortest TTL retry queue
	err = channel.PublishWithContext(
		ctx,
		"",                                  // exchange (direct to queue)
		"notification_work_queue.retry.1.0s", // routing key
		false,                               // mandatory
		false,                               // immediate
		amqp.Publishing{
			ContentType: "text/plain",
			Body:        []byte("retry test message"),
		},
	)
	require.NoError(t, err)

	// Consume from the work queue
	msgs, err := channel.Consume(
		"notification_work_queue", // queue
		"",                        // consumer
		true,                      // auto-ack
		false,                     // exclusive
		false,                     // no-local
		false,                     // no-wait
		nil,                       // args
	)
	require.NoError(t, err)

	// Wait for the message to be routed after the TTL expires
	select {
	case msg := <-msgs:
		assert.Equal(t, "retry test message", string(msg.Body))
	case <-time.After(2 * time.Second): // Wait a bit longer than the TTL
		t.Fatal("Timed out waiting for message to be routed from retry queue")
	}
}

// TestPartitionDecreaseIntegration tests the partition decrease functionality with a real RabbitMQ instance
func TestPartitionDecreaseIntegration(t *testing.T) {
	// Skip if not running integration tests
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Partition decrease functionality has been re-enabled with the new channel management approach

	// Set up test environment
	ctx := context.Background()

	// Start RabbitMQ container
	rabbitmqContainer, connectionConfig, err := startRabbitmqContainer(ctx)
	require.NoError(t, err)
	defer func() {
		if err := rabbitmqContainer.Terminate(ctx); err != nil {
			t.Logf("Failed to terminate container: %v", err)
		}
	}()

	// Create connection
	conn, err := rabbitmq.NewConnection(connectionConfig)
	require.NoError(t, err)
	defer conn.Close()

	// Test scenario: Start with 5 partitions, then decrease to 3
	t.Run("DecreaseFrom5To3Partitions", func(t *testing.T) {
		// Create fresh channel for this test
		channel, err := conn.Channel()
		require.NoError(t, err)
		defer channel.Close()

		// Create exchange
		exchangeName := "test-partition-decrease-5to3-exchange"
		err = channel.ExchangeDeclare(
			exchangeName, // name
			"topic",      // type
			true,         // durable
			false,        // auto-deleted
			false,        // internal
			false,        // no-wait
			nil,          // arguments
		)
		require.NoError(t, err)

		testPartitionDecrease(t, ctx, conn, channel, exchangeName, 5, 3)
	})

	// Test scenario: Start with 3 partitions, then decrease to 1
	t.Run("DecreaseFrom3To1Partition", func(t *testing.T) {
		// Create fresh channel for this test
		channel, err := conn.Channel()
		require.NoError(t, err)
		defer channel.Close()

		// Create exchange
		exchangeName := "test-partition-decrease-3to1-exchange"
		err = channel.ExchangeDeclare(
			exchangeName, // name
			"topic",      // type
			true,         // durable
			false,        // auto-deleted
			false,        // internal
			false,        // no-wait
			nil,          // arguments
		)
		require.NoError(t, err)

		testPartitionDecrease(t, ctx, conn, channel, exchangeName, 3, 1)
	})
}

// testPartitionDecrease is a helper function that tests decreasing partitions from initialCount to finalCount
func testPartitionDecrease(t *testing.T, ctx context.Context, conn *rabbitmq.Connection, channel *amqp.Channel, exchangeName string, initialCount, finalCount int) {
	baseQueueName := fmt.Sprintf("test-partition-decrease-%dto%d", initialCount, finalCount)
	topics := []string{"test.topic"}

	// Step 1: Create initial topology with higher partition count
	t.Logf("Creating initial topology with %d partitions", initialCount)
	initialTopology := NewPartitionedQueueDefinition(exchangeName, baseQueueName, initialCount, topics)

	// Setup the initial topology
	err := initialTopology.Setup(conn)
	require.NoError(t, err)

	// Verify all initial partitions were created
	initialPartitions := initialTopology.GetPartitionQueueNames()
	require.Len(t, initialPartitions, initialCount)

	for _, queueName := range initialPartitions {
		queue, err := channel.QueueDeclarePassive(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		require.NoError(t, err)
		assert.Equal(t, queueName, queue.Name)
		t.Logf("Verified initial partition queue: %s", queueName)
	}

	// Step 2: Add some test messages to the queues that will be removed
	messagesToAdd := 3
	queuesToReceiveMessages := initialPartitions[finalCount:] // Queues that will be removed

	t.Logf("Adding %d test messages to queues that will be removed: %v", messagesToAdd, queuesToReceiveMessages)

	for _, queueName := range queuesToReceiveMessages {
		for i := 0; i < messagesToAdd; i++ {
			err := channel.PublishWithContext(
				ctx,
				"",        // exchange (direct to queue)
				queueName, // routing key
				false,     // mandatory
				false,     // immediate
				amqp.Publishing{
					ContentType: "text/plain",
					Body:        []byte(fmt.Sprintf("test message %d for queue %s", i+1, queueName)),
					Headers: amqp.Table{
						rabbitmq.OriginalRoutingKey: "test.topic",
					},
				},
			)
			require.NoError(t, err)
		}
	}

	// Wait for messages to be delivered
	time.Sleep(100 * time.Millisecond)

	// Verify messages are in the queues
	for _, queueName := range queuesToReceiveMessages {
		queueInfo, err := channel.QueueDeclarePassive(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		require.NoError(t, err)
		assert.Equal(t, messagesToAdd, queueInfo.Messages, "Queue %s should have %d messages", queueName, messagesToAdd)
		t.Logf("Queue %s has %d messages as expected", queueName, queueInfo.Messages)
	}

	// Step 3: Create new topology with reduced partition count
	t.Logf("Creating new topology with %d partitions (decrease from %d)", finalCount, initialCount)
	finalTopology := NewPartitionedQueueDefinition(exchangeName, baseQueueName, finalCount, topics)

	// Setup the final topology (this should trigger partition decrease)
	err = finalTopology.Setup(conn)
	require.NoError(t, err)

	// Step 4: Verify the correct number of partitions remain
	finalPartitions := finalTopology.GetPartitionQueueNames()
	require.Len(t, finalPartitions, finalCount)

	// Verify remaining partitions still exist
	for _, queueName := range finalPartitions {
		queue, err := channel.QueueDeclarePassive(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		require.NoError(t, err)
		assert.Equal(t, queueName, queue.Name)
		t.Logf("Verified remaining partition queue: %s", queueName)
	}

	// Step 5: Verify removed partitions no longer exist
	// Create a fresh channel for verification to avoid channel closure issues
	verificationChannel, err := conn.Channel()
	require.NoError(t, err)
	defer verificationChannel.Close()

	removedPartitions := initialPartitions[finalCount:]
	for _, queueName := range removedPartitions {
		_, err := verificationChannel.QueueDeclarePassive(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		assert.Error(t, err, "Queue %s should have been deleted", queueName)
		// Accept both NOT_FOUND and channel closure as valid indicators that the queue was deleted
		errStr := err.Error()
		isDeleted := strings.Contains(errStr, "NOT_FOUND") || strings.Contains(errStr, "channel/connection is not open") || strings.Contains(errStr, "504")
		assert.True(t, isDeleted, "Queue %s should return NOT_FOUND or channel closure error, got: %s", queueName, errStr)
		t.Logf("Verified partition queue was removed: %s", queueName)
	}

	// Step 6: Verify messages were moved to router exchange by checking if they can be redistributed
	// We'll consume from the router exchange to see if messages were moved there
	routerExchangeName := finalTopology.GetRouterExchangeName()

	// Create another fresh channel for temporary queue operations
	tempChannel, err := conn.Channel()
	require.NoError(t, err)
	defer tempChannel.Close()

	// Create a temporary queue to capture redistributed messages
	tempQueueName := "temp-redistribution-test"
	_, err = tempChannel.QueueDeclare(
		tempQueueName, // name
		false,         // durable
		true,          // delete when unused
		false,         // exclusive
		false,         // no-wait
		nil,           // arguments
	)
	require.NoError(t, err)

	// Bind temp queue to router exchange with a numeric routing key for consistent hash
	err = tempChannel.QueueBind(
		tempQueueName,        // queue name
		"1",                  // routing key (numeric for consistent hash)
		routerExchangeName,   // exchange
		false,                // no-wait
		nil,                  // arguments
	)
	require.NoError(t, err)

	t.Logf("Created temporary queue %s bound to router exchange %s", tempQueueName, routerExchangeName)
}

// TestPartitionDecreaseWithMessagesIntegration tests partition decrease with message redistribution
func TestPartitionDecreaseWithMessagesIntegration(t *testing.T) {
	// Skip if not running integration tests
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Partition decrease functionality has been re-enabled with the new channel management approach

	// Set up test environment
	ctx := context.Background()

	// Start RabbitMQ container
	rabbitmqContainer, connectionConfig, err := startRabbitmqContainer(ctx)
	require.NoError(t, err)
	defer func() {
		if err := rabbitmqContainer.Terminate(ctx); err != nil {
			t.Logf("Failed to terminate container: %v", err)
		}
	}()

	// Create connection
	conn, err := rabbitmq.NewConnection(connectionConfig)
	require.NoError(t, err)
	defer conn.Close()

	// Create channel
	channel, err := conn.Channel()
	require.NoError(t, err)
	defer channel.Close()

	// Create exchange
	exchangeName := "test-message-redistribution-exchange"
	err = channel.ExchangeDeclare(
		exchangeName, // name
		"topic",      // type
		true,         // durable
		false,        // auto-deleted
		false,        // internal
		false,        // no-wait
		nil,          // arguments
	)
	require.NoError(t, err)

	baseQueueName := "test-message-redistribution"
	topics := []string{"test.message"}
	initialPartitionCount := 4
	finalPartitionCount := 2

	// Step 1: Create initial topology with 4 partitions
	t.Logf("Creating initial topology with %d partitions", initialPartitionCount)
	initialTopology := NewPartitionedQueueDefinition(exchangeName, baseQueueName, initialPartitionCount, topics)

	err = initialTopology.Setup(conn)
	require.NoError(t, err)

	// Step 2: Add messages to the queues that will be removed (partitions 3 and 4)
	initialPartitions := initialTopology.GetPartitionQueueNames()
	queuesToRemove := initialPartitions[finalPartitionCount:] // Last 2 queues
	messagesPerQueue := 5

	t.Logf("Adding %d messages to each queue that will be removed: %v", messagesPerQueue, queuesToRemove)

	totalMessagesAdded := 0
	for _, queueName := range queuesToRemove {
		for i := 0; i < messagesPerQueue; i++ {
			messageBody := fmt.Sprintf("Message %d for queue %s", i+1, queueName)
			err := channel.PublishWithContext(
				ctx,
				"",        // exchange (direct to queue)
				queueName, // routing key
				false,     // mandatory
				false,     // immediate
				amqp.Publishing{
					ContentType: "application/json",
					Body:        []byte(messageBody),
					Headers: amqp.Table{
						rabbitmq.OriginalRoutingKey: "test.message",
					},
				},
			)
			require.NoError(t, err)
			totalMessagesAdded++
		}
	}

	// Wait for messages to be delivered
	time.Sleep(200 * time.Millisecond)

	// Verify messages are in the queues before decrease
	for _, queueName := range queuesToRemove {
		queueInfo, err := channel.QueueDeclarePassive(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		require.NoError(t, err)
		assert.Equal(t, messagesPerQueue, queueInfo.Messages, "Queue %s should have %d messages before decrease", queueName, messagesPerQueue)
	}

	t.Logf("Total messages added to queues that will be removed: %d", totalMessagesAdded)

	// Step 3: Note the initial message counts in remaining queues before partition decrease
	finalTopology := NewPartitionedQueueDefinition(exchangeName, baseQueueName, finalPartitionCount, topics)
	finalPartitions := finalTopology.GetPartitionQueueNames()

	initialMessageCounts := make(map[string]int)
	for _, queueName := range finalPartitions {
		queueInfo, err := channel.QueueDeclarePassive(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		require.NoError(t, err)
		initialMessageCounts[queueName] = queueInfo.Messages
		t.Logf("Queue %s has %d messages before partition decrease", queueName, queueInfo.Messages)
	}

	// Step 4: Create new topology with reduced partition count (this triggers partition decrease)
	t.Logf("Creating new topology with %d partitions (decrease from %d)", finalPartitionCount, initialPartitionCount)

	err = finalTopology.Setup(conn)
	require.NoError(t, err)

	// Step 5: Wait a moment for message redistribution to complete
	time.Sleep(500 * time.Millisecond)

	// Step 6: Verify message redistribution by checking final message counts in remaining queues
	finalMessageCounts := make(map[string]int)
	totalFinalMessages := 0

	for _, queueName := range finalPartitions {
		queueInfo, err := channel.QueueDeclarePassive(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		require.NoError(t, err)
		finalMessageCounts[queueName] = queueInfo.Messages
		totalFinalMessages += queueInfo.Messages
		t.Logf("Queue %s has %d messages after partition decrease (was %d)", queueName, queueInfo.Messages, initialMessageCounts[queueName])
	}

	// Verify that the total number of messages in remaining queues equals the initial total plus redistributed messages
	totalInitialMessages := 0
	for _, count := range initialMessageCounts {
		totalInitialMessages += count
	}

	expectedTotalMessages := totalInitialMessages + totalMessagesAdded
	assert.Equal(t, expectedTotalMessages, totalFinalMessages,
		"Total messages in remaining queues should equal initial messages (%d) plus redistributed messages (%d)",
		totalInitialMessages, totalMessagesAdded)

	// Step 7: Verify removed queues no longer exist
	removedPartitions := initialPartitions[finalPartitionCount:]

	for _, queueName := range removedPartitions {
		_, err := channel.QueueDeclarePassive(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		assert.Error(t, err, "Queue %s should have been deleted", queueName)
		// Accept both NOT_FOUND and channel closure as valid indicators that the queue was deleted
		errStr := err.Error()
		isDeleted := strings.Contains(errStr, "NOT_FOUND") || strings.Contains(errStr, "channel/connection is not open") || strings.Contains(errStr, "504")
		assert.True(t, isDeleted, "Queue %s should return NOT_FOUND or channel closure error, got: %s", queueName, errStr)
		t.Logf("Verified partition queue was removed: %s", queueName)
	}

	// Step 8: Verify remaining queues still exist
	// Create a fresh channel for final verification to avoid channel closure issues
	finalVerificationChannel, err := conn.Channel()
	require.NoError(t, err)
	defer finalVerificationChannel.Close()

	for _, queueName := range finalPartitions {
		queue, err := finalVerificationChannel.QueueDeclarePassive(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		require.NoError(t, err)
		assert.Equal(t, queueName, queue.Name)
		t.Logf("Verified remaining partition queue: %s", queueName)
	}

	t.Logf("Successfully completed partition decrease test with message redistribution")
}

// TestPartitionDecreaseEdgeCasesIntegration tests edge cases for partition decrease
func TestPartitionDecreaseEdgeCasesIntegration(t *testing.T) {
	// Skip if not running integration tests
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Partition decrease functionality has been re-enabled with the new channel management approach

	// Set up test environment
	ctx := context.Background()

	// Start RabbitMQ container
	rabbitmqContainer, connectionConfig, err := startRabbitmqContainer(ctx)
	require.NoError(t, err)
	defer func() {
		if err := rabbitmqContainer.Terminate(ctx); err != nil {
			t.Logf("Failed to terminate container: %v", err)
		}
	}()

	// Create connection
	conn, err := rabbitmq.NewConnection(connectionConfig)
	require.NoError(t, err)
	defer conn.Close()

	t.Run("NoDecreaseNeeded", func(t *testing.T) {
		// Create fresh channel for this test
		channel, err := conn.Channel()
		require.NoError(t, err)
		defer channel.Close()

		// Create exchange
		exchangeName := "test-no-decrease-exchange"
		err = channel.ExchangeDeclare(
			exchangeName, // name
			"topic",      // type
			true,         // durable
			false,        // auto-deleted
			false,        // internal
			false,        // no-wait
			nil,          // arguments
		)
		require.NoError(t, err)

		// Test case: Same partition count should not trigger decrease
		baseQueueName := "test-no-decrease"
		topics := []string{"test.topic"}
		partitionCount := 3

		// Create topology
		topology := NewPartitionedQueueDefinition(exchangeName, baseQueueName, partitionCount, topics)

		// Setup twice with same partition count
		err = topology.Setup(conn)
		require.NoError(t, err)

		err = topology.Setup(conn)
		require.NoError(t, err)

		// Verify all partitions still exist
		partitions := topology.GetPartitionQueueNames()
		require.Len(t, partitions, partitionCount)

		for _, queueName := range partitions {
			queue, err := channel.QueueDeclarePassive(
				queueName, // name
				true,      // durable
				false,     // delete when unused
				false,     // exclusive
				false,     // no-wait
				nil,       // arguments
			)
			require.NoError(t, err)
			assert.Equal(t, queueName, queue.Name)
		}

		t.Log("No decrease case passed - topology remains unchanged")
	})

	t.Run("IncreasePartitions", func(t *testing.T) {
		// Create fresh channel for this test
		channel, err := conn.Channel()
		require.NoError(t, err)
		defer channel.Close()

		// Create exchange
		exchangeName := "test-increase-exchange"
		err = channel.ExchangeDeclare(
			exchangeName, // name
			"topic",      // type
			true,         // durable
			false,        // auto-deleted
			false,        // internal
			false,        // no-wait
			nil,          // arguments
		)
		require.NoError(t, err)

		// Test case: Increasing partitions should not trigger decrease logic
		baseQueueName := "test-increase"
		topics := []string{"test.topic"}

		// Start with 2 partitions
		initialTopology := NewPartitionedQueueDefinition(exchangeName, baseQueueName, 2, topics)
		err = initialTopology.Setup(conn)
		require.NoError(t, err)

		// Increase to 4 partitions
		finalTopology := NewPartitionedQueueDefinition(exchangeName, baseQueueName, 4, topics)
		err = finalTopology.Setup(conn)
		require.NoError(t, err)

		// Verify all 4 partitions exist
		partitions := finalTopology.GetPartitionQueueNames()
		require.Len(t, partitions, 4)

		for _, queueName := range partitions {
			queue, err := channel.QueueDeclarePassive(
				queueName, // name
				true,      // durable
				false,     // delete when unused
				false,     // exclusive
				false,     // no-wait
				nil,       // arguments
			)
			require.NoError(t, err)
			assert.Equal(t, queueName, queue.Name)
		}

		t.Log("Increase case passed - all partitions created")
	})

	t.Run("EmptyQueuesDecrease", func(t *testing.T) {
		// Create fresh channel for this test
		channel, err := conn.Channel()
		require.NoError(t, err)
		defer channel.Close()

		// Create exchange
		exchangeName := "test-empty-decrease-exchange"
		err = channel.ExchangeDeclare(
			exchangeName, // name
			"topic",      // type
			true,         // durable
			false,        // auto-deleted
			false,        // internal
			false,        // no-wait
			nil,          // arguments
		)
		require.NoError(t, err)

		// Test case: Decreasing partitions with empty queues
		baseQueueName := "test-empty-decrease"
		topics := []string{"test.topic"}

		// Start with 3 partitions
		initialTopology := NewPartitionedQueueDefinition(exchangeName, baseQueueName, 3, topics)
		err = initialTopology.Setup(conn)
		require.NoError(t, err)

		// Decrease to 1 partition (no messages in queues)
		finalTopology := NewPartitionedQueueDefinition(exchangeName, baseQueueName, 1, topics)
		err = finalTopology.Setup(conn)
		require.NoError(t, err)

		// Verify only 1 partition remains
		partitions := finalTopology.GetPartitionQueueNames()
		require.Len(t, partitions, 1)

		// Verify the remaining partition exists
		queue, err := channel.QueueDeclarePassive(
			partitions[0], // name
			true,          // durable
			false,         // delete when unused
			false,         // exclusive
			false,         // no-wait
			nil,           // arguments
		)
		require.NoError(t, err)
		assert.Equal(t, partitions[0], queue.Name)

		// Verify removed partitions no longer exist
		removedQueues := []string{
			fmt.Sprintf("%s.work.2", baseQueueName),
			fmt.Sprintf("%s.work.3", baseQueueName),
		}

		for _, queueName := range removedQueues {
			_, err := channel.QueueDeclarePassive(
				queueName, // name
				true,      // durable
				false,     // delete when unused
				false,     // exclusive
				false,     // no-wait
				nil,       // arguments
			)
			assert.Error(t, err, "Queue %s should have been deleted", queueName)
			// Accept both NOT_FOUND and channel closure as valid indicators that the queue was deleted
			errStr := err.Error()
			isDeleted := strings.Contains(errStr, "NOT_FOUND") || strings.Contains(errStr, "channel/connection is not open") || strings.Contains(errStr, "504")
			assert.True(t, isDeleted, "Queue %s should return NOT_FOUND or channel closure error, got: %s", queueName, errStr)
		}

		t.Log("Empty queues decrease case passed")
	})
}
