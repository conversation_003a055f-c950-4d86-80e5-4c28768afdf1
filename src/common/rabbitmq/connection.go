package rabbitmq

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/cockroachdb/errors"
	amqp "github.com/rabbitmq/amqp091-go"
)

const (
	ReconnectInterval = 5 * time.Second
)

// Connection represents a RabbitMQ connection
type Connection struct {
	conn          *amqp.Connection
	mutex         sync.RWMutex
	name          string
	amqpURL       string
	ctx           context.Context
	cancel        context.CancelFunc
	reconnecting  bool
	closeListener chan *amqp.Error
	logger        *slog.Logger
}

// ConnectionConfig holds the configuration for a RabbitMQ connection
type ConnectionConfig struct {
	Name     string
	Username string
	Password string
	Host     string
	Port     string
	VHost    string
}

// NewConnection creates a new Connection instance
func NewConnection(config ConnectionConfig) (*Connection, error) {
	connectionLogger := slog.With(
		"component", "rabbitmq",
		"host", config.Host,
		"port", config.Port,
		"vhost", config.VHost,
	)

	// Construct the AMQP URL from config
	amqpURL := fmt.Sprintf("amqp://%s:%s@%s:%s%s",
		config.Username,
		config.Password,
		config.Host,
		config.Port,
		config.VHost)

	ctx, cancel := context.WithCancel(context.Background())

	connection := &Connection{
		name:          config.Name,
		amqpURL:       amqpURL,
		ctx:           ctx,
		cancel:        cancel,
		closeListener: make(chan *amqp.Error, 1),
		logger:        connectionLogger,
	}

	// Connect to RabbitMQ
	if err := connection.connect(); err != nil {
		cancel()
		connectionLogger.Error("Failed to establish initial connection", "error", err)
		return nil, errors.Wrap(err, "failed to establish initial connection to RabbitMQ")
	}

	// Start monitoring for connection failures
	go connection.monitorConnection()

	connectionLogger.Info("Connection successfully established")
	return connection, nil
}

// connect establishes a connection to RabbitMQ
func (c *Connection) connect() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	connectLogger := c.logger.With("operation", "connect")

	// Close existing connection if it exists
	if c.conn != nil {
		connectLogger.Debug("Closing existing connection before reconnect")
		c.conn.Close()
		c.conn = nil
	}

	var err error

	// Connect to RabbitMQ
	connectLogger.Debug("Attempting to connect to RabbitMQ")

	conf := amqp.Config{
		Properties: amqp.Table{},
	}

	if c.name != "" {
		conf.Properties["connection_name"] = c.name
	}

	c.conn, err = amqp.DialConfig(c.amqpURL, conf)
	if err != nil {
		connectLogger.Error("Failed to connect", "error", err)
		return errors.Wrap(err, "failed to connect to RabbitMQ")
	}

	// Setup the close notification channel
	c.closeListener = make(chan *amqp.Error, 1)
	c.conn.NotifyClose(c.closeListener)

	connectLogger.Info("Successfully connected to RabbitMQ")
	return nil
}

// monitorConnection watches for disconnection and reconnects as needed
func (c *Connection) monitorConnection() {
	monitorLogger := c.logger.With("operation", "monitorConnection")
	monitorLogger.Debug("Starting connection monitor")

	for {
		select {
		case err, ok := <-c.closeListener:
			if !ok || err == nil {
				// Channel closed normally or connection was closed
				monitorLogger.Info("Connection monitor stopping due to normal closure")
				return
			}

			monitorLogger.Warn("RabbitMQ connection closed unexpectedly",
				"error", err,
				"code", err.Code,
				"reason", err.Reason,
				"server", err.Server)

			c.reconnecting = true
			attempts := 0
			for {
				attempts++
				monitorLogger.Info("Attempting to reconnect",
					"attempt", attempts,
					"interval", ReconnectInterval)

				if err := c.connect(); err != nil {
					monitorLogger.Error("Failed to reconnect",
						"attempt", attempts,
						"error", err,
						"nextRetryIn", ReconnectInterval)
					time.Sleep(ReconnectInterval)
					continue
				}

				monitorLogger.Info("Successfully reconnected", "attempts", attempts)
				break
			}
			c.reconnecting = false

		case <-c.ctx.Done():
			// Context was canceled, exit the goroutine
			monitorLogger.Info("Connection monitor stopping due to context cancellation")
			return
		}
	}
}

// Channel creates a new channel from the connection
func (c *Connection) Channel() (*amqp.Channel, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	channelLogger := c.logger.With("operation", "Channel")

	if c.conn == nil || c.conn.IsClosed() {
		channelLogger.Error("Attempted to create channel with no active connection")
		return nil, errors.New("no active connection to RabbitMQ")
	}

	channelLogger.Debug("Creating new channel")
	channel, err := c.conn.Channel()
	if err != nil {
		channelLogger.Error("Failed to create channel", "error", err)
		return nil, errors.Wrap(err, "failed to create channel from connection")
	}

	channelLogger.Debug("Successfully created channel")
	return channel, nil
}

// Close closes the connection and cancels the monitoring goroutine
func (c *Connection) Close() error {
	closeLogger := c.logger.With("operation", "Close")
	closeLogger.Info("Closing connection")

	c.cancel() // Stop the monitoring goroutine

	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		closeLogger.Debug("Closing AMQP connection")
		err := c.conn.Close()
		if err != nil {
			closeLogger.Error("Error closing connection", "error", err)
			c.conn = nil
			return errors.Wrap(err, "error closing AMQP connection")
		}
		c.conn = nil
		closeLogger.Debug("AMQP connection closed successfully")
	} else {
		closeLogger.Debug("Connection already closed")
	}

	closeLogger.Info("Connection closed completely")
	return nil
}

// IsConnected returns whether the connection is currently established
func (c *Connection) IsConnected() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.conn != nil && !c.reconnecting
}
