package channel_pool

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/cockroachdb/errors"
	amqp "github.com/rabbitmq/amqp091-go"
)

// DefaultConfirmTimeout is the default timeout for waiting for confirmation
const DefaultConfirmTimeout = 10 * time.Second

// ReturnError represents an error when a message is returned
type ReturnError struct {
	ReplyCode uint16
	ReplyText string
}

func (e *ReturnError) Error() string {
	return fmt.Sprintf("message returned: [%d] %s", e.ReplyCode, e.ReplyText)
}

// Channel defines the interface for a publishing channel
type Channel interface {
	// Close closes the underlying channel
	Close() error

	// IsClosed returns true if the channel is closed
	IsClosed() bool

	// Channel returns the underlying amqp.Channel
	Channel() *amqp.Channel

	// PublishWithContext publishes a message and waits for confirmation or return if confirm mode is enabled
	PublishWithContext(
		ctx context.Context,
		exchange, key string,
		mandatory, immediate bool,
		msg amqp.Publishing,
	) error
}

// publishingChannel is a wrapper around amqp.Channel that handles confirms and returns
type publishingChannel struct {
	ch          *amqp.Channel
	returns     chan amqp.Return
	logger      *slog.Logger
	confirmMode bool
}

// NewPublishingChannel creates a new Channel implementation
func NewPublishingChannel(ch *amqp.Channel, enableConfirms bool) (Channel, error) {
	pc := &publishingChannel{
		ch:          ch,
		logger:      slog.With("component", "publishing-channel"),
		returns:     ch.NotifyReturn(make(chan amqp.Return, 100)),
		confirmMode: enableConfirms,
	}

	// Enable confirm mode if requested
	if enableConfirms {
		if err := ch.Confirm(false); err != nil {
			return nil, errors.Wrap(err, "failed to put channel in confirm mode")
		}
	}

	return pc, nil
}

func (pc *publishingChannel) Close() error {
	return pc.ch.Close()
}

func (pc *publishingChannel) IsClosed() bool {
	return pc.ch.IsClosed()
}

// Channel returns the underlying amqp.Channel
func (pc *publishingChannel) Channel() *amqp.Channel {
	return pc.ch
}

// PublishWithContext publishes a message and waits for confirmation or return if confirm mode is enabled
func (pc *publishingChannel) PublishWithContext(
	ctx context.Context,
	exchange, key string,
	mandatory, immediate bool,
	msg amqp.Publishing,
) error {
	logger := pc.logger.With(
		"operation", "PublishWithContext",
		"exchange", exchange,
		"key", key,
	)

	// Create a child context with timeout to prevent indefinite waiting
	childCtx, cancel := context.WithTimeout(ctx, DefaultConfirmTimeout)
	defer cancel()

	// Publish the message
	confirmation, err := pc.ch.PublishWithDeferredConfirmWithContext(
		childCtx,
		exchange,
		key,
		mandatory,
		immediate,
		msg,
	)

	if err != nil {
		logger.Error("Failed to publish message", "error", err)
		return errors.Wrap(err, "failed to publish message")
	}

	// If confirm mode is not enabled, return immediately
	if !pc.confirmMode {
		return nil
	}

	// Check for returns if mandatory or immediate flags are set
	if mandatory || immediate {
		select {
		case ret := <-pc.returns:
			logger.Warn("Message returned by server",
				"code", ret.ReplyCode,
				"text", ret.ReplyText)
			return &ReturnError{
				ReplyCode: ret.ReplyCode,
				ReplyText: ret.ReplyText,
			}
		default:
			// No return received, proceed with confirmation
		}
	}

	// Wait for confirmation with context
	acked, err := confirmation.WaitContext(childCtx)
	if err != nil {
		logger.Error("Context canceled or timed out waiting for confirmation", "error", err)
		return errors.Wrap(err, "waiting for confirmation")
	}

	if !acked {
		logger.Error("Server did not acknowledge message", "deliveryTag", confirmation.DeliveryTag)
		return errors.New("server negatively acknowledged message")
	}

	logger.Debug("Message confirmed by server", "deliveryTag", confirmation.DeliveryTag)
	return nil
}
