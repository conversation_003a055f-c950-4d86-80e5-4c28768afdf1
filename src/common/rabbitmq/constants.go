package rabbitmq

import (
	"time"
)

// Constants for retry mechanism and message headers
const (
	RetryCountHeader   = "x-retry-count"
	FirstTryHeader     = "x-first-try"
	LastTryHeader      = "x-last-try"
	DLQReasonHeader    = "x-dlq-reason"
	OriginalRoutingKey = "x-original-routing-key"

	ConsistentHashHeader = "consistent-hash-key"
	LastRetryReason      = "x-last-retry-reason"

	DefaultPublishTimeout = 5 * time.Second
)
