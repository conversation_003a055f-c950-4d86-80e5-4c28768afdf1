package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetConsumerStartupSleepEnabled(t *testing.T) {
	// Save original value to restore later
	originalValue := os.Getenv("CONSUMER_STARTUP_SLEEP_ENABLED")
	defer func() {
		if originalValue == "" {
			os.Unsetenv("CONSUMER_STARTUP_SLEEP_ENABLED")
		} else {
			os.Setenv("CONSUMER_STARTUP_SLEEP_ENABLED", originalValue)
		}
	}()

	tests := []struct {
		name     string
		envValue string
		expected bool
		unset    bool
	}{
		{
			name:     "Default when unset",
			unset:    true,
			expected: false,
		},
		{
			name:     "Empty string",
			envValue: "",
			expected: false,
		},
		{
			name:     "True string",
			envValue: "true",
			expected: true,
		},
		{
			name:     "False string",
			envValue: "false",
			expected: false,
		},
		{
			name:     "True with different case",
			envValue: "TRUE",
			expected: true,
		},
		{
			name:     "False with different case",
			envValue: "FALSE",
			expected: false,
		},
		{
			name:     "Numeric true",
			envValue: "1",
			expected: true,
		},
		{
			name:     "Numeric false",
			envValue: "0",
			expected: false,
		},
		{
			name:     "Invalid value defaults to false",
			envValue: "invalid",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.unset {
				os.Unsetenv("CONSUMER_STARTUP_SLEEP_ENABLED")
			} else {
				os.Setenv("CONSUMER_STARTUP_SLEEP_ENABLED", tt.envValue)
			}

			result := GetConsumerStartupSleepEnabled()
			assert.Equal(t, tt.expected, result)
		})
	}
}
